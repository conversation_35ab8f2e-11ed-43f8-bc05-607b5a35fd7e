---
title: 人才福利 测试
language_tabs:
  - shell: Shell
  - http: HTTP
  - javascript: JavaScript
  - ruby: Ruby
  - python: Python
  - php: PHP
  - java: Java
  - go: Go
toc_footers: []
includes: []
search: true
code_clipboard: true
highlight_theme: darkula
headingLevel: 2
generator: "@tarslib/widdershins v4.0.30"

---

# 人才福利 测试

Base URLs:

# Authentication

* API Key (Authorization)
    - Parameter Name: **Authorization**, in: header. 

# 活动管理（优惠券）

<a id="opIdupdate_7"></a>

## PUT 修改

PUT /sys/operation/activity

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "beginTime": "2019-08-24T14:15:22Z",
  "endTime": "2019-08-24T14:15:22Z",
  "perCapita": 0,
  "peopleCount": 0,
  "status": "UNPUBLISHED",
  "revision": 0,
  "count": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[OperationActivity](#schemaoperationactivity)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|Bad Request|[R](#schemar)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdadd_10"></a>

## POST 添加

POST /sys/operation/activity

> Body 请求参数

```json
{
  "id": 0,
  "name": "string",
  "beginTime": "2019-08-24T14:15:22Z",
  "endTime": "2019-08-24T14:15:22Z",
  "perCapita": 0,
  "peopleCount": 0,
  "status": "UNPUBLISHED",
  "revision": 0,
  "count": 0
}
```

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|body|body|[OperationActivity](#schemaoperationactivity)| 否 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|Bad Request|[R](#schemar)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdchange_1"></a>

## PUT 上下架

PUT /sys/operation/activity/change/{id}/{status}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |none|
|status|path|string| 是 |none|

#### 枚举值

|属性|值|
|---|---|
|status|UNPUBLISHED|
|status|PUBLISHED|
|status|REMOVE_SHELVES|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|Bad Request|[R](#schemar)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIdpageInfo_10"></a>

## GET 分页

GET /sys/operation/activity/pageInfo

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|current|query|integer(int32)| 是 |当前页|
|size|query|integer(int32)| 是 |每页数量|
|name|query|string| 否 |none|
|status|query|string| 否 |none|

#### 枚举值

|属性|值|
|---|---|
|status|UNPUBLISHED|
|status|PUBLISHED|
|status|REMOVE_SHELVES|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":{"size":0,"records":[{"id":0,"name":"string","beginTime":"2019-08-24T14:15:22Z","endTime":"2019-08-24T14:15:22Z","perCapita":0,"peopleCount":0,"status":"UNPUBLISHED","revision":0,"count":0}],"current":0,"total":0,"pages":0},"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RIPageOperationActivity](#schemaripageoperationactivity)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|Bad Request|[R](#schemar)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

<a id="opIddel_8"></a>

## DELETE 删除

DELETE /sys/operation/activity/{id}

### 请求参数

|名称|位置|类型|必选|说明|
|---|---|---|---|---|
|id|path|integer(int64)| 是 |none|

> 返回示例

> 200 Response

```
{"code":0,"msg":"string","data":true,"ok":true}
```

### 返回结果

|状态码|状态码含义|说明|数据模型|
|---|---|---|---|
|200|[OK](https://tools.ietf.org/html/rfc7231#section-6.3.1)|OK|[RBoolean](#schemarboolean)|
|400|[Bad Request](https://tools.ietf.org/html/rfc7231#section-6.5.1)|Bad Request|[R](#schemar)|
|403|[Forbidden](https://tools.ietf.org/html/rfc7231#section-6.5.3)|Forbidden|[R](#schemar)|
|500|[Internal Server Error](https://tools.ietf.org/html/rfc7231#section-6.6.1)|Internal Server Error|[R](#schemar)|

# 数据模型

<h2 id="tocS_R">R</h2>

<a id="schemar"></a>
<a id="schema_R"></a>
<a id="tocSr"></a>
<a id="tocsr"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {},
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|object|false|none||数据|
|ok|boolean|false|none||none|

<h2 id="tocS_RBoolean">RBoolean</h2>

<a id="schemarboolean"></a>
<a id="schema_RBoolean"></a>
<a id="tocSrboolean"></a>
<a id="tocsrboolean"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": true,
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|boolean|false|none||数据|
|ok|boolean|false|none||none|

<h2 id="tocS_OperationActivity">OperationActivity</h2>

<a id="schemaoperationactivity"></a>
<a id="schema_OperationActivity"></a>
<a id="tocSoperationactivity"></a>
<a id="tocsoperationactivity"></a>

```json
{
  "id": 0,
  "name": "string",
  "beginTime": "2019-08-24T14:15:22Z",
  "endTime": "2019-08-24T14:15:22Z",
  "perCapita": 0,
  "peopleCount": 0,
  "status": "UNPUBLISHED",
  "revision": 0,
  "count": 0
}

```

一键领券活动表

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|id|integer(int64)|false|none||唯一标识|
|name|string|true|none||活动名称|
|beginTime|string(date-time)|true|none||开始时间|
|endTime|string(date-time)|true|none||结束时间|
|perCapita|integer(int32)|true|none||人均次数|
|peopleCount|integer(int32)|true|none||支持领券人数|
|status|string|false|none||状态|
|revision|integer(int32)|false|none||乐观锁|
|count|integer(int64)|false|none||已领取数量|

#### 枚举值

|属性|值|
|---|---|
|status|UNPUBLISHED|
|status|PUBLISHED|
|status|REMOVE_SHELVES|

<h2 id="tocS_IPageOperationActivity">IPageOperationActivity</h2>

<a id="schemaipageoperationactivity"></a>
<a id="schema_IPageOperationActivity"></a>
<a id="tocSipageoperationactivity"></a>
<a id="tocsipageoperationactivity"></a>

```json
{
  "size": 0,
  "records": [
    {
      "id": 0,
      "name": "string",
      "beginTime": "2019-08-24T14:15:22Z",
      "endTime": "2019-08-24T14:15:22Z",
      "perCapita": 0,
      "peopleCount": 0,
      "status": "UNPUBLISHED",
      "revision": 0,
      "count": 0
    }
  ],
  "current": 0,
  "total": 0,
  "pages": 0
}

```

数据

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|size|integer(int64)|false|none||none|
|records|[[OperationActivity](#schemaoperationactivity)]|false|none||[一键领券活动表]|
|current|integer(int64)|false|none||none|
|total|integer(int64)|false|none||none|
|pages|integer(int64)|false|none||none|

<h2 id="tocS_RIPageOperationActivity">RIPageOperationActivity</h2>

<a id="schemaripageoperationactivity"></a>
<a id="schema_RIPageOperationActivity"></a>
<a id="tocSripageoperationactivity"></a>
<a id="tocsripageoperationactivity"></a>

```json
{
  "code": 0,
  "msg": "string",
  "data": {
    "size": 0,
    "records": [
      {
        "id": 0,
        "name": "string",
        "beginTime": "2019-08-24T14:15:22Z",
        "endTime": "2019-08-24T14:15:22Z",
        "perCapita": 0,
        "peopleCount": 0,
        "status": "UNPUBLISHED",
        "revision": 0,
        "count": 0
      }
    ],
    "current": 0,
    "total": 0,
    "pages": 0
  },
  "ok": true
}

```

响应信息主体

### 属性

|名称|类型|必选|约束|中文名|说明|
|---|---|---|---|---|---|
|code|integer(int32)|false|none||返回标记：成功标记=0，失败标记=1|
|msg|string|false|none||返回信息|
|data|[IPageOperationActivity](#schemaipageoperationactivity)|false|none||数据|
|ok|boolean|false|none||none|

