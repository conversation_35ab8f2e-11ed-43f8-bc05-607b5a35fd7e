import { defHttp } from '@/utils/http/axios';

enum Api {
  ApartmentPage = '/sys/operation/apartment/pageInfo',
  Apartment = '/sys/operation/apartment',
  ApartmentId = '/sys/operation/apartment/{id}',
}

export const apiApartmentPage = (params: any) => defHttp.get({ url: Api.ApartmentPage, params });

export const apiApartmentAdd = (data: any) =>
  defHttp.post(
    { url: Api.Apartment, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiApartmentEdit = (data: any) =>
  defHttp.put(
    { url: Api.Apartment, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiApartmentDelete = (id: any) =>
  defHttp.delete(
    { url: Api.ApartmentId.replace('{id}', id) },
    {
      successMessageMode: 'message',
    },
  );

export const apiApartmentInfo = ({ id }: any) =>
  defHttp.get({ url: Api.ApartmentId.replace('{id}', id) });
