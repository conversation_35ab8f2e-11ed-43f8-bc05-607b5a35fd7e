import { defHttp, downloadHttp } from '@/utils/http/axios';

enum Api {
  AuditPage = '/sys/operation/audit/page',
  AuditHandle = '/sys/operation/audit/handle',
  AuditExport = '/sys/operation/audit/export',
  AduitDetail = '/sys/operation/audit/detail/{id}',
  AduitCollect = '/sys/operation/audit/collect',
  AduitHighFrequency = '/sys/operation/audit/highFrequency',
  AduitStatistics = '/sys/operation/audit/statistics',
}

export const apiAuditPage = (params: any) => defHttp.get({ url: Api.AuditPage, params });

export const apiAuditHandle = (data: any) =>
  defHttp.post(
    { url: Api.AuditHandle, data },
    {
      successMessageMode: 'message',
    },
  );

/**
 * try download this
 * attachment;filename*=utf-8''2025-06-19T14%3A56%3A10.282389078.xlsx
 */
export const apiAuditExport = (params: any) =>
  downloadHttp.get({
    url: Api.AuditExport,
    params,
    responseType: 'blob',
  });

export const apiAduitDetail = ({ id }: any) =>
  defHttp.get({ url: Api.AduitDetail.replace('{id}', id) });

export const apiAduitCollect = (params: any) => {
  return defHttp.get({ url: Api.AduitCollect, params });
};

export const apiAduitHighFrequency = () => {
  return defHttp.get({ url: Api.AduitHighFrequency });
};

export const apiAduitStatistics = () => {
  return defHttp.get({ url: Api.AduitStatistics });
};

export interface IStatistics {
  name: string;
  code: string;
  count: number;
}
