import { defHttp } from '@/utils/http/axios';

enum Api {
  CategoryPage = '/sys/operation/category/pageInfo',
  Category = '/sys/operation/category',
  CategoryId = '/sys/operation/category/{id}',
  CategoryList = '/sys/operation/category/list',
}

export const apiCategoryPage = (params: any) => defHttp.get({ url: Api.CategoryPage, params });

export const apiCategoryAdd = (data: any) =>
  defHttp.post(
    { url: Api.Category, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiCategoryEdit = (data: any) =>
  defHttp.put(
    { url: Api.Category, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiCategoryDelete = (id: any) =>
  defHttp.delete(
    { url: Api.CategoryId.replace('{id}', id) },
    {
      successMessageMode: 'message',
    },
  );

export const apiCategoryList = (params: any) => defHttp.get({ url: Api.CategoryList, params });
