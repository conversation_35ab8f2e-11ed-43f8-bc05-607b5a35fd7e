import { defHttp } from '@/utils/http/axios';

enum Api {
  Consult = '/sys/operation/consult',
  ConsultPage = '/sys/operation/consult/pageInfo',
  ConsultId = '/sys/operation/consult/{id}',
}

export const apiConsultPage = (params: any) => defHttp.get({ url: Api.ConsultPage, params });

export const apiConsultAdd = (data: any) =>
  defHttp.post(
    { url: Api.Consult, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiConsultEdit = (data: any) =>
  defHttp.put(
    { url: Api.Consult, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiConsultDel = ({ id }: any) =>
  defHttp.delete(
    { url: Api.ConsultId.replace('{id}', id) },
    {
      successMessageMode: 'message',
    },
  );

export const apiConsultInfo = (id: any) => defHttp.get({ url: Api.ConsultId.replace('{id}', id) });
