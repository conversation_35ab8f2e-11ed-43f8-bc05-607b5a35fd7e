import { defHttp, downloadHttp } from '@/utils/http/axios';

enum Api {
  Coupon = '/sys/operation/coupon',
  CouponPage = '/sys/operation/coupon/pageInfo',
  CouponId = '/sys/operation/coupon/{id}',
  CouponGrantPage = '/sys/operation/coupon/grant/pageInfo',
  CouponGrantId = '/sys/operation/coupon/grant/{id}',
  CouponGrantMemberListId = '/sys/operation/coupon/grant/memberListByGrantId/{id}',
  CouponOperate = '/sys/operation/coupon/operate/{id}/{state}',
  CouponGrantmemberListByCouponId = '/sys/operation/coupon/memberListByCouponId/{couponId}',
  CouponBatchRelease = '/sys/operation/member/coupon/batchRelease',
  CouponExport = '/sys/operation/member/coupon/export',
  CouponCollectPage = '/sys/operation/member/coupon/pageInfo',
}

export const apiCouponPage = (params: any) => defHttp.get({ url: Api.CouponPage, params });

export const apiCouponAdd = (data: any) =>
  defHttp.post(
    { url: Api.Coupon, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiCouponEdit = (data: any) =>
  defHttp.put(
    {
      url: Api.Coupon,
      data,
    },
    {
      successMessageMode: 'message',
    },
  );

export const apiCouponDelete = (id: any) =>
  defHttp.delete(
    {
      url: Api.CouponId.replace('{id}', id),
    },
    {
      successMessageMode: 'message',
    },
  );

export const apiCouponInfo = ({ id }: any) =>
  defHttp.get({ url: Api.CouponId.replace('{id}', id) });

export const apiCouponGrantPage = (params: any) =>
  defHttp.get({ url: Api.CouponGrantPage, params });

export const apiCouponGrantId = ({ id }: any) => {
  return defHttp.get({
    url: Api.CouponGrantId.replace('{id}', id),
  });
};
export const apiCouponGrantMemberListIdPage = ({ id, ...params }: any) => {
  return defHttp.get({
    url: Api.CouponGrantMemberListId.replace('{id}', id),
    params,
  });
};

export const apiCouponGrantmemberListByCouponIdPage = ({ id, ...params }: any) => {
  return defHttp.get({
    url: Api.CouponGrantmemberListByCouponId.replace('{couponId}', id),
    params,
  });
};

export const apiCouponOperate = ({ id, state }: any) => {
  return defHttp.put(
    {
      url: Api.CouponOperate.replace('{id}', id).replace('{state}', state),
    },
    {
      successMessageMode: 'message',
    },
  );
};

export const apiCouponBatchRelease = (data: any) => {
  return defHttp.put(
    {
      url: Api.CouponBatchRelease,
      data,
    },
    {
      successMessageMode: 'message',
    },
  );
};

export const apiCouponExport = (params: any) => {
  return downloadHttp.get({
    url: Api.CouponExport,
    params,
    responseType: 'blob',
  });
};

export const apiCouponCollectPage = (params: any) => {
  return defHttp.get({ url: Api.CouponCollectPage, params });
};
