import { defHttp } from '@/utils/http/axios';

enum Api {
  Enterprise = '/sys/operation/enterprise',
  EnterprisePage = '/sys/operation/enterprise/pageInfo',
  EnterpriseId = '/sys/operation/enterprise/{id}',
}

export const apiEnterpriseAdd = (data: any) =>
  defHttp.post(
    { url: Api.Enterprise, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiEnterpriseEdit = (data: any) =>
  defHttp.put(
    {
      url: Api.Enterprise,
      data,
    },
    {
      successMessageMode: 'message',
    },
  );

export const apiEnterprisePage = (params: any) =>
  defHttp.get({
    url: Api.EnterprisePage,
    params,
  });

export const apiEnterpriseDelete = (id: any) =>
  defHttp.delete(
    {
      url: Api.EnterpriseId.replace('{id}', id),
    },
    {
      successMessageMode: 'message',
    },
  );

export const apiEnterpriseInfo = (id: any) =>
  defHttp.get({
    url: Api.EnterpriseId.replace('{id}', id),
  });
