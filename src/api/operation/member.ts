import { defHttp } from '@/utils/http/axios';

enum Api {
  MemberPage = '/sys/operation/member/page',
  MemberId = '/sys/operation/member/{id}',
  MemberCouponPage = '/sys/operation/member/coupon/pageInfo',
  MemberCouponBatchSettle = '/sys/operation/member/coupon/batchSettle',
  MemberCouponWriteOffPage = '/sys/operation/member/coupon/writeOffPage',
  MemberBlacklist = '/sys/operation/member/changeState/{id}/{state}',
}

export const apiMemberPage = (params: any) => defHttp.get({ url: Api.MemberPage, params });

export const apiMemberInfo = ({ id }: any) =>
  defHttp.get({ url: Api.MemberId.replace('{id}', id) }, {});

export const apiMemberCouponPage = (params: any) =>
  defHttp.get({ url: Api.MemberCouponPage, params });

export const apiMemberCouponBatchSettle = (data: any) => {
  return defHttp.put(
    { url: Api.MemberCouponBatchSettle, data },
    {
      successMessageMode: 'message',
    },
  );
};

export const apiMemberCouponWriteOffPage = (params: any) => {
  return defHttp.get({ url: Api.MemberCouponWriteOffPage, params });
};

export const apiMemberBlacklist = ({ id, state }: { id: string; state: boolean }) => {
  return defHttp.put({
    url: Api.MemberBlacklist.replace('{id}', id).replace('{state}', state + ''),
  });
};
