import { defHttp } from '@/utils/http/axios';

enum Api {
  Policy = '/sys/operation/policy',
  PolicyPage = '/sys/operation/policy/pageInfo',
  PolicyId = '/sys/operation/policy/{id}',
}

export const apiPolicyPage = (params: any) => defHttp.get({ url: Api.PolicyPage, params });

export const apiPolicyAdd = (data: any) =>
  defHttp.post(
    { url: Api.Policy, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiPolicyEdit = (data: any) =>
  defHttp.put(
    { url: Api.Policy, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiPolicyDel = (id: any) =>
  defHttp.delete(
    { url: Api.PolicyId.replace('{id}', id) },
    {
      successMessageMode: 'message',
    },
  );

export const apiPolicyInfo = (params: any) => defHttp.get({ url: Api.Policy, params });
