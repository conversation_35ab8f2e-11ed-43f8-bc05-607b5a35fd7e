import { defHttp } from '@/utils/http/axios';

enum Api {
  Poster = '/sys/operation/poster',
  PosterPage = '/sys/operation/poster/pageInfo',
  PosterId = '/sys/operation/poster/{id}',
}

export const apiPosterPage = (params: any) => defHttp.get({ url: Api.PosterPage, params });

export const apiPosterAdd = (data: any) =>
  defHttp.post(
    { url: Api.Poster, data },
    {
      successMessageMode: 'message',
    },
  );
export const apiPosterDel = (id: any) =>
  defHttp.delete(
    { url: Api.PosterId.replace('{id}', id) },
    {
      successMessageMode: 'message',
    },
  );
