import { defHttp } from '@/utils/http/axios';

enum Api {
  StorePage = '/sys/operation/store/pageInfo',
  Store = '/sys/operation/store',
  StoreId = '/sys/operation/store/{id}',
  StoreList = '/sys/operation/store/list',
  StoreInfoId = '/sys/operation/store/info/{id}',
  StoreImport = '/sys/operation/store/import',
  StorePutAway = '/sys/operation/store/putAway/{id}/{state}',
}

export const apiStorePage = (params: any) => defHttp.get({ url: Api.StorePage, params });

export const apiStoreAdd = (data: any) =>
  defHttp.post(
    { url: Api.Store, data },
    {
      successMessageMode: 'message',
    },
  );

export const apiStoreEdit = (data: any) =>
  defHttp.put({ url: Api.Store, data }, { successMessageMode: 'message' });

export const apiStoreDelete = ({ id }: any) =>
  defHttp.delete({ url: Api.StoreId.replace('{id}', id) }, { successMessageMode: 'message' });

export const apiStoreList = (params: any) => defHttp.get({ url: Api.StoreList, params });

export const apiStoreInfo = ({ id }: any) =>
  defHttp.get({ url: Api.StoreInfoId.replace('{id}', id) });

export const apiStoreImport = (params: any, config?: any) =>
  defHttp.uploadFile({ url: Api.StoreImport, timeout: 1000 * 60 * 10, ...config }, params);

// 上下架
export const apiStorePutAway = ({ id, state }: any) =>
  defHttp.put(
    { url: Api.StorePutAway.replace('{id}', id).replace('{state}', state) },
    { successMessageMode: 'message' },
  );
