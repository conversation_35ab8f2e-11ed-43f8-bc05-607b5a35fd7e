import { defHttp } from '@/utils/http/axios';
import type { AxiosRequestConfig } from 'axios';

enum Api {
  FilePage = '/sys/file/page',
  FileUpload = '/sys/file/upload',
  File = '/sys/file',
}

export const apiFilePage = (params: any) => defHttp.get({ url: Api.FilePage, params });

export const apiUpload = (params: any, config: AxiosRequestConfig = {}) =>
  defHttp.uploadFile(
    {
      url: Api.FileUpload,
      ...config,
      timeout: 1000 * 60 * 10, // 10分钟
    },
    params,
  );

export const apiFileDelete = (data: any) => defHttp.delete({ url: Api.File, data });
