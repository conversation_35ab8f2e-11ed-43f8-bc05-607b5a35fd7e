import { defHttp } from '@/utils/http/axios';
import { getMenuListResultModel } from './model/menuModel';

enum Api {
  GetMenuList = '/sys/menu/tree',
  Menu = '/sys/menu',
  MenuAdd = '/sys/menu/add',
  MenuEdit = '/sys/menu/edit',
  TreeByRole = '/sys/menu/treeByRole',
}

/**
 * @description: Get user menu based on id
 */

export const getMenuList = () => {
  return defHttp.get<getMenuListResultModel>({ url: Api.GetMenuList });
};

export const getMenuByUser = () => {
  return defHttp.get<getMenuListResultModel>({ url: Api.Menu });
};

export const getTreeByRole = (data) => {
  return defHttp.post<getMenuListResultModel>({ url: Api.TreeByRole, data });
};

/**
 * @description: 新增菜单
 */
export const addSysMenu = (data: any) => {
  return defHttp.post({ url: Api.MenuAdd, data }, { successMessageMode: 'message' });
};

/**
 * @description: 编辑菜单
 */
export const editSysMenu = (data: any) => {
  return defHttp.put({ url: Api.MenuEdit, data }, { successMessageMode: 'message' });
};

/**
 * @description: 删除
 */
export const deleteSysMenu = ({ id }: any) => {
  return defHttp.delete({ url: `${Api.Menu}/${id}` }, { successMessageMode: 'message' });
};
