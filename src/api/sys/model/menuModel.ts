// import type { RouteMeta } from 'vue-router';

export interface RouteItem {
  id: string;
  parentId: string;
  weight: number;
  name: string;
  path: string;
  component: string | null;
  meta: MenuMeta;
  sortOrder: number;
  menuType: string;
  permission: string | null;
  children?: RouteItem[];
}

export interface MenuMeta {
  isLink: string;
  isIframe: boolean | null;
  isKeepAlive: boolean;
  icon: string;
  isAffix: boolean;
  title: string;
  isHide: boolean;
}

/**
 * @description: Get menu return value
 */
export type getMenuListResultModel = RouteItem[];
