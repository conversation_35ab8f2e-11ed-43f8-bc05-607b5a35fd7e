import { defHttp } from '@/utils/http/axios';

enum Api {
  RolePage = '/sys/role/page',
  Role = '/sys/role',
  RoleAdd = '/sys/role/ad',
  RoleEdit = '/sys/role/edit',
  RoleMenu = '/sys/role/roleMenu',
}

/**
 * @description: 角色列表
 */

export const getRolePage = (params: any) => {
  return defHttp.get({ url: Api.RolePage, params });
};

/**
 * @description: 新增角色
 */
export const createRole = (data: any) => {
  return defHttp.post({ url: Api.RoleAdd, data });
};

/**
 * @description: 编辑角色
 */
export const updateRole = (data: any) => {
  return defHttp.put({ url: Api.RoleEdit, data });
};

/**
 * @description: 删除角色
 */
export const deleteRole = ({ id }: any) => {
  return defHttp.delete({ url: `${Api.Role}/${id}` });
};
/**
 * @description: 分配菜单
 */
export const putMenuByRole = (data: any) => {
  return defHttp.put({ url: Api.RoleMenu, data });
};
/**
 * @description: 根据id获取已分配菜单
 */
export const getMenuByRole = ({ id }: any) => {
  return defHttp.get({ url: `${Api.RoleMenu}/${id}` });
};
