import { defHttp } from '@/utils/http/axios';
import { LoginParams, LoginResultModel, GetUserInfoModel } from './model/userModel';
import { useGlobSetting } from '@/hooks/setting';
import { ErrorMessageMode } from '#/axios';
// import { encryption } from '@/utils/cipher';

enum Api {
  Login = '/oauth2/token',
  Logout = '/auth/logout',
  GetUserInfo = '/sys/user/loginInfo',
  GetPermCode = '/getPermCode',
  User = '/sys/user',
  UserChangePass = '/sys/user/changePass',
  UserPage = '/sys/user/page',
  UserAdd = '/sys/user/add',
  UserEdit = '/sys/user/edit',
  HomePersonalPassword = '/sys/home/<USER>/password',
}

/**
 * @description: user login api
 */
export function loginApi(params: LoginParams, mode: ErrorMessageMode = 'modal') {
  const { basicAuth } = useGlobSetting();
  // const dataObj = encryption({
  //   key: 'aparthouseencodekeys',
  //   param: ['password'],
  //   data: params,
  // });
  return defHttp.post<LoginResultModel>(
    {
      url: Api.Login,
      params: params,
      headers: {
        Authorization: `Basic ${btoa(basicAuth!)}`,
      },
    },
    {
      errorMessageMode: mode,
    },
  );
}

/**
 * @description: getUserInfo
 */
export function getUserInfo() {
  return defHttp.get<GetUserInfoModel>({ url: Api.GetUserInfo }, { errorMessageMode: 'none' });
}

export function getPermCode() {
  return defHttp.get<string[]>({ url: Api.GetPermCode });
}

export function doLogout() {
  return defHttp.delete({ url: Api.Logout });
}

/**
 * @description: 用户列表
 */
export function getUserPage(params) {
  return defHttp.get({ url: Api.UserPage, params });
}

/**
 * @description: 创建用户
 */
export function createUser(data: any) {
  return defHttp.post({ url: Api.UserAdd, data }, { successMessageMode: 'message' });
}

/**
 * @description: 编辑用户信息
 */
export function editUser(data: any) {
  return defHttp.put({ url: Api.UserEdit, data }, { successMessageMode: 'message' });
}

/**
 * @description: 删除用户
 */
export function deleteUser({ id }: any) {
  return defHttp.delete({ url: `${Api.User}/${id}` }, { successMessageMode: 'message' });
}

/**
 * @description: 修改密码
 */

export function changePassword(params: any) {
  return defHttp.post({ url: Api.UserChangePass, params }, { successMessageMode: 'message' });
}

export function changePersonalPassword(data: any) {
  return defHttp.put({ url: Api.HomePersonalPassword, data }, { successMessageMode: 'message' });
}
