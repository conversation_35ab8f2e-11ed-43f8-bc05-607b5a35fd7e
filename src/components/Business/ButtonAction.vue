<template>
  <div>
    <a-space>
      <slot name="before"></slot>
      <a-button
        preIcon="ant-design:arrow-left-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        size="default"
        @click="closeDrawer()"
        v-if="showCancel"
      >
        {{ cancelText }}
      </a-button>
      <slot name="center"></slot>
      <a-button
        preIcon="ant-design:check-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        size="default"
        :loading="loading"
        type="primary"
        @click="handlerSubmit"
        v-if="showSave"
      >
        {{ saveText }}
      </a-button>
      <slot></slot>
    </a-space>
  </div>
</template>

<script lang="ts" setup>
  const loading = defineModel('loading', {
    type: Boolean,
    default: false,
  });
  const saveText = defineModel('saveText', {
    type: String,
    default: '保存',
  });

  // 取消文字
  const cancelText = defineModel('cancelText', {
    type: String,
    default: '返回',
  });

  const emit = defineEmits(['submit', 'cancel']);

  const showCancel = defineModel('showCancel', {
    type: Boolean,
    default: true,
  });
  const showSave = defineModel('showSave', {
    type: Boolean,
    default: true,
  });

  const handlerSubmit = () => {
    emit('submit');
  };

  const closeDrawer = () => {
    emit('cancel');
  };

  const propsConfig = {
    buttonStyle: 'min-width: 110px',
  };
</script>

<style lang="less" scoped></style>
