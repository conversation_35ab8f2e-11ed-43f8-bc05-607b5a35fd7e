<template>
  <div class="flex flex-wrap gap-4">
    <div
      v-for="option in options"
      :key="option.value"
      class="px-4 py-1.5 text-sm rounded cursor-pointer transition-all hover:bg-[rgba(56,128,115,0.14)]"
      :class="{
        'bg-[rgba(56,128,115,0.10)] text-#388073': value === option.value,
        'bg-#F2F3F5 text-#4E5969': value !== option.value,
      }"
      @click="change(option.value)"
    >
      {{ option.label }}
    </div>
  </div>
</template>

<script setup lang="tsx">
  import { cloneDeep } from 'lodash-es';

  const value = defineModel<string | number>('value');
  const { options = [] } = defineProps<{
    options: { value: string | number; label: string | number }[];
  }>();
  const emits = defineEmits<{
    change: [value: string | number];
  }>();

  /**
   * ====================
   *       基本逻辑
   * ====================
   */
  /**
   * 更新值
   * @param val 值
   */
  function change(val: string | number) {
    value.value = cloneDeep(val);
    emits('change', cloneDeep(val));
  }
</script>

<style lang="less" scoped></style>
