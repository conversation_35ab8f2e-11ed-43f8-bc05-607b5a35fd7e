<template>
  <div class="InputTextAreaAudit">
    <Dropdown>
      <div class="InputTextAreaAudit__label">
        <div> 快捷备注 </div>
      </div>

      <template #overlay>
        <div v-loading="loading">
          <Menu class="InputTextAreaAudit--menu" @click="handleMenuClick">
            <Menu.Item v-for="(item, index) in apiResult" :key="index">
              <div class="InputTextAreaAudit__menu-item" :title="item">
                {{ item }}
              </div>
            </Menu.Item>
          </Menu>
        </div>
      </template>
    </Dropdown>

    <Textarea v-bind="getBindValue" v-model:value="remarkValueRef" ref="textareaRef" />
  </div>
</template>

<script lang="ts" setup>
  import { Textarea, TextAreaProps, Dropdown, Menu } from 'ant-design-vue';
  import { ComponentOptionsMixin, computed, unref, ref, watchEffect } from 'vue';
  import { apiAduitHighFrequency } from '@/api/operation/audit';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { nth, omit } from 'lodash-es';

  defineOptions({
    name: 'InputTextAreaAudit',
    extends: Textarea as ComponentOptionsMixin,
    inheritAttrs: false,
  });

  const props = withDefaults(defineProps<TextAreaProps>(), {
    bordered: true,
  });

  const getBindValue = computed(() => ({
    ...omit(props, ['onChange', 'value']),
  }));

  const remarkValueRef = ref(props.value);
  watchEffect(() => {
    const value = remarkValueRef.value;
    // @ts-expect-error ignore
    props.onChange?.(value);
  });

  const { loading, apiResult } = useApiLoading({
    api: apiAduitHighFrequency,
  });
  const textareaRef = ref();

  const handleMenuClick = (event: any) => {
    const { key } = event;
    const selectedText = nth(unref(apiResult), key); // 获取选中的文本
    if (selectedText !== undefined) {
      const textarea = textareaRef.value?.$el.querySelector('textarea');
      if (textarea) {
        textarea.value = selectedText;
        textarea.dispatchEvent(new Event('input', { bubbles: true }));
      }
    }
  };
</script>

<style lang="less" scoped>
  .InputTextAreaAudit {
    position: relative;

    .InputTextAreaAudit__label {
      position: absolute;
      top: -30px;
      right: 10px;
      color: @primary-color;
      cursor: pointer;
      user-select: none;

      &:hover {
        color: @primary-color-hover;
      }
    }

    &--menu {
      min-width: 50px;
      min-height: 50px;
      max-height: 400px;
      overflow-y: auto;

      &-item {
        display: block;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // 鼠标悬浮的时候显示全部内容
      }
    }
  }
</style>
