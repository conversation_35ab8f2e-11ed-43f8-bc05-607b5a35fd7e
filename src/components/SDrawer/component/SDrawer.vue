<template>
  <Drawer
    v-model:open="visible"
    v-bind="$attrs"
    :width="width"
    :closable="closable"
    :rootStyle="{
      position: 'absolute',
    }"
    :content-wrapper-style="{
      maxWidth: '100%',
    }"
    :bodyStyle="{
      padding: 0,
    }"
  >
    <template #extra>
      <slot name="extra"></slot>
    </template>

    <div class="sdrawer-box">
      <slot></slot>
    </div>

    <div class="sdrawer-button">
      <a-space :size="propsConfig.space">
        <slot name="insertButton" v-bind="propsConfig"></slot>
        <a-button preIcon="ant-design:arrow-left-outlined" v-bind="propsConfig" @click="cancel">
          {{ cancelText }}
        </a-button>
        <slot name="button" v-bind="propsConfig"></slot>
        <a-button :preIcon="okIcon" type="primary" v-bind="propsConfig" @click="ok" v-if="isOk">
          {{ okText }}
        </a-button>
        <slot name="beforeButton" v-bind="propsConfig"></slot>
      </a-space>
    </div>
  </Drawer>
</template>

<script lang="ts">
  import { ref, getCurrentInstance, defineComponent, reactive } from 'vue';
  import { Modal, Drawer } from 'ant-design-vue';
  import { DrawerInstance } from '../types/SDrawer';
  import { useI18n } from '@/hooks/web/useI18n';

  const { t } = useI18n();

  export default defineComponent({
    name: 'SDrawer',
    components: { Drawer },
    props: {
      isOk: {
        type: Boolean,
        default: true,
      },
      cancelText: {
        type: String,
        default: t('component.s_drawer.btn_cancel'),
      },
      okText: {
        type: String,
        default: t('component.s_drawer.btn_confirm'),
      },
      okIcon: {
        type: String,
        default: 'ant-design:check-outlined',
      },
      width: {
        type: String,
        default: '100%',
      },
      closable: {
        type: Boolean,
        default: false,
      },
      loading: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['register', 'onOk', 'onCancel', 'update:open'],
    setup(_props: any, { emit }) {
      const visible = ref<boolean>(false);
      const propsConfig = reactive({
        // 组件默认props
        size: 'large',
        style: 'min-width: 120px',
        space: 15,
        iconSize: 18,
      });
      const instance = getCurrentInstance();
      const sDrawerInstance: DrawerInstance = {
        openDrawer,
        closeDrawer,
        errorDrawer,
      };
      instance && emit('register', sDrawerInstance, instance.uid);

      function openDrawer() {
        emit('update:open', true);
        visible.value = true;
      }
      function closeDrawer() {
        emit('update:open', false);
        visible.value = false;
      }

      function errorDrawer(content = '异常错误，请联系开发者') {
        Modal.error({
          title: '错误',
          content: content,
          onOk() {
            closeDrawer();
          },
          centered: true,
        });
      }
      function ok() {
        emit('onOk');
      }
      function cancel() {
        closeDrawer();
        emit('onCancel');
      }

      return {
        openDrawer,
        closeDrawer,
        propsConfig,
        ok,
        cancel,
        visible,
      };
    },
  });
</script>

<style lang="less" scoped>
  .sdrawer {
    position: absolute;
    // visibility: hidden;
    z-index: 100;
    top: 0;
    right: 0;
    bottom: 0;
    overflow: auto;
    transition: all 0.3s;
    border-left: #dfdfdf solid 1px;
    // background: #546b8e;
    background: #f0f2f5;

    &-box {
      position: relative;
      width: 100%;
      height: 100%;
      padding: 20px 20px 80px;
      overflow: auto;
    }

    &-button {
      display: flex;
      position: absolute;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      align-items: center;
      width: 100%;
      height: 60px;
      padding: 0 20px;
      border-top: #eee solid 1px;
      background-color: #ffffff84;
    }
  }
</style>
