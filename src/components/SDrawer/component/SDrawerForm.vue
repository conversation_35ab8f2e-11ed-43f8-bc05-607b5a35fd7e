<template>
  <Drawer
    v-model:open="visible"
    :bodyStyle="{
      padding: 0,
    }"
    :rootStyle="{
      position: 'absolute',
    }"
    :content-wrapper-style="{
      maxWidth: '100%',
    }"
    v-bind="$attrs"
    :width="width"
    :closable="closable"
    :destroyOnClose="propsConfig.destroyOnClose"
    :title="getTitle"
  >
    <div class="sdrawerForm-box">
      <div>
        <div class="px-35px pt-30px pb-40px flex items-center justify-center flex-col">
          <BasicForm class="max-w-800px w-100%" @register="registerForm" @submit="onSubmit" />
        </div>
      </div>
    </div>
    <div class="sdrawerForm-button">
      <a-button
        preIcon="ant-design:arrow-left-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        size="default"
        @click="closeDrawer()"
      >
        {{ t('component.s_drawer_form.btn_cancel') }}
      </a-button>
      <a-button
        preIcon="ant-design:check-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="18"
        size="default"
        :loading="loading"
        type="primary"
        @click="handlerSubmit"
      >
        {{ t('component.s_drawer_form.btn_save') }}
      </a-button>
    </div>
  </Drawer>
</template>

<script lang="ts">
  import {
    ref,
    getCurrentInstance,
    defineComponent,
    computed,
    unref,
    reactive,
    nextTick,
  } from 'vue';
  import { isEmpty, isObject, isFunction, cloneDeep } from 'lodash-es';
  import { DrawerInstance, OpenDrawerData, SDrawerForm } from '../types/SDrawerForm';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { message, Drawer } from 'ant-design-vue';
  import { useI18n } from '@/hooks/web/useI18n';

  export default defineComponent({
    name: 'SDrawer',
    components: { BasicForm, Drawer },
    props: {
      width: {
        type: String,
        default: '550px',
      },
      closable: {
        type: Boolean,
        default: false,
      },
    },
    emits: ['register', 'success'],
    setup(_props: any, { emit }) {
      const visible = ref<boolean>(false);
      const loading = ref<boolean>(false);
      const isUpdate = ref(false);
      const instance = getCurrentInstance();
      const { t } = useI18n();
      // const SDrawerFormConfig =  as SDrawerForm;

      let registerForm, formMethod, addFn, updateFn, merge;
      const propsConfig = reactive({
        layout: 'vertical',
        autoFocusFirstItem: true,
        baseColProps: { span: 24 },
        showActionButtonGroup: false,
        // 组件默认props
        buttonStyle: 'width: 110px',
        addText: t('component.s_drawer_form.title_add'),
        updateText: t('component.s_drawer_form.title_edit'),
        destroyOnClose: false,
      }) as SDrawerForm;

      const getTitle = computed(() => {
        return unref(isUpdate) ? propsConfig.updateText : propsConfig.addText;
      });
      const sDrawerInstance: DrawerInstance = {
        openDrawer,
        init,
      };
      instance && emit('register', sDrawerInstance, instance.uid);

      function openDrawer(data: OpenDrawerData) {
        visible.value = true;
        const { isUpdate: _isUpdate, record: _record, beforeFn, title } = data;
        merge = data.merge;
        isUpdate.value = _isUpdate;
        if (title && typeof title === 'string' && _isUpdate) {
          propsConfig.updateText = title;
        } else if (title && typeof title === 'string' && !_isUpdate) {
          propsConfig.addText = title;
        }

        nextTick(async () => {
          try {
            await formMethod.resetFields();
            beforeFn && (await beforeFn(formMethod, data));
            if (!isEmpty(_record)) {
              formMethod.setFieldsValue(_record);
            }
          } catch (error) {
            console.error(error);
          }
        });
      }
      async function handlerSubmit() {
        try {
          if (formMethod?.submit) {
            await formMethod.submit();
          } else {
            message.error('fromMethod.submit 不存在于 SDrawerFrom 情况联系开发者解决异常');
          }
        } catch (error) {
          message.error(t('component.s_drawer_form.msg_error_from'));
        }
      }
      async function onSubmit(value) {
        try {
          loading.value = true;
          if (!isEmpty(merge) && isObject(merge)) {
            Object.assign(value, merge);
          } else if (isFunction(merge)) {
            value = (await merge(cloneDeep(value))) || value;
          }
          if (unref(isUpdate)) {
            await updateFn(value);
          } else {
            await addFn(value);
          }
          closeDrawer();
          emit('success', {
            isUpdate: unref(isUpdate),
            value,
          });
        } catch (error) {
          console.error(error);
        } finally {
          loading.value = false;
        }
      }
      function closeDrawer() {
        visible.value = false;
      }
      function init(propsForm: SDrawerForm) {
        addFn = propsForm.addFn;
        updateFn = propsForm.updateFn;
        if (propsForm) {
          Object.assign(propsConfig, propsForm);
          [registerForm, formMethod] = useForm(propsConfig);
        }
      }

      return {
        closeDrawer,
        visible,
        registerForm,
        formMethod,
        getTitle,
        propsConfig,
        loading,
        handlerSubmit,
        onSubmit,
        t,
      };
    },
  });
</script>

<style lang="less" scoped>
  .sdrawerForm {
    &-box {
      width: 100%;
      height: 100%;
      padding-bottom: 80px;
      overflow: auto;
    }

    &-button {
      display: flex;
      position: absolute;
      z-index: 1;
      bottom: 0;
      left: 0;
      box-sizing: border-box;
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      height: 60px;
      padding: 0 20px;
      border-top: #eee8 solid 1px;
      background-color: @component-background-alpha;
      box-shadow:
        0 -6px 16px -8px rgb(0 0 0 / 4%),
        0 -9px 28px 0 rgb(0 0 0 / 3%),
        0 -12px 48px 16px rgb(0 0 0 / 2%);
      gap: 15px;
      // 背景模糊
      backdrop-filter: blur(2px);
    }
  }
</style>
