import { ref, unref, getCurrentInstance, reactive, toRaw, watchEffect, nextTick } from 'vue';
import { isProdMode } from '@/utils/env';
import { isFunction } from '@/utils/is';
import { tryOnUnmounted } from '@vueuse/core';
import { DrawerInstance, UseDrawerReturnType, UseDrawerInnerReturnType } from '../types/SDrawer';

const dataTransferRef = reactive<any>({});
export function useSDrawer(): UseDrawerReturnType {
  const refSTable = ref<DrawerInstance | null>(null);
  const uid = ref<string>('');
  function register(drawerInstance: DrawerInstance, uuid: string) {
    isProdMode() &&
      tryOnUnmounted(() => {
        dataTransferRef[unref(uid)] = null;
        refSTable.value = null;
      });
    uid.value = uuid;
    refSTable.value = drawerInstance;
  }
  function getInstance() {
    const instance = unref(refSTable);
    if (!instance) {
      console.error('useStable instance is undefined!');
    }
    return instance;
  }
  const methods = {
    openDrawer: <T = any>(data?: T) => {
      getInstance()?.openDrawer();
      if (data) {
        dataTransferRef[unref(uid)] = toRaw(data);
      } else if (dataTransferRef[unref(uid)]) {
        dataTransferRef[unref(uid)] = null;
      }
    },
    closeDrawer: () => {
      getInstance()?.closeDrawer();
    },
  };
  return [register, methods];
}

export function useSDrawerInner(callbackFn?: Fn): UseDrawerInnerReturnType {
  const sDrawerInstanceRef = ref<DrawerInstance | null>(null);
  const currentInstance = getCurrentInstance();
  const uidRef = ref<string>('');
  if (!currentInstance) {
    throw new Error('useDrawerInner() can only be used inside setup() or functional components!');
  }
  const getInstance = () => {
    const instance = unref(sDrawerInstanceRef);
    if (!instance) {
      console.error('useDrawerInner instance is undefined!');
      return;
    }
    return instance;
  };
  const register = (modalInstance: DrawerInstance, uuid: string) => {
    isProdMode() &&
      tryOnUnmounted(() => {
        sDrawerInstanceRef.value = null;
      });
    uidRef.value = uuid;
    sDrawerInstanceRef.value = modalInstance;
    currentInstance.emit('register', modalInstance, uuid);
  };
  watchEffect(() => {
    const data = dataTransferRef[unref(uidRef)];
    if (!data) return;
    if (!callbackFn || !isFunction(callbackFn)) return;
    nextTick(() => {
      callbackFn(data);
    });
  });
  return [
    register,
    {
      closeDrawer: () => {
        getInstance()?.closeDrawer();
      },
      setLoading: (bl = false) => {
        getInstance()?.setLoading(bl);
      },
      setPageLoading: (bl = false) => {
        getInstance()?.setPageLoading(bl);
      },
      errorDrawer: (text?: string) => {
        getInstance()?.errorDrawer(text);
      },
    },
  ];
}
