<template>
  <Modal :title="getTitle" width="600px" v-model:open="visible" v-bind="$attrs" centered>
    <div class="p-8">
      <Description @register="registerDesc" />
    </div>
    <template #footer>
      <a-button
        preIcon="ant-design:close-outlined"
        :style="propsConfig.buttonStyle"
        :iconSize="16"
        @click="closeModal()"
        v-if="isClose"
      >
        {{ closeText }}
      </a-button>
    </template>
  </Modal>
</template>

<script lang="ts">
  import { defineComponent, ref, reactive, getCurrentInstance } from 'vue';
  import { Modal } from 'ant-design-vue';
  import { SModalDesInstance, OpenModalDesData, SModalDesProps } from '../types/typing';
  import { Description, useDescription } from '@/components/Description/index';

  export default defineComponent({
    name: 'SModalView',
    components: { Description, Modal },
    props: {
      isClose: {
        type: <PERSON>olean,
        default: true,
      },
      closeText: {
        type: String,
        default: '关闭',
      },
    },
    emits: ['register', 'onClose'],
    setup(_props: any, { emit }) {
      const instance = getCurrentInstance();
      let visible = ref<boolean>(false);
      let registerDesc, descMethod;
      const propsConfig = reactive({
        size: 'small',
        column: 1,
        labelStyle: {
          width: '180px',
        },
      }) as SModalDesProps;
      // const propsConfig = reactive({
      //   size: 'small',
      //   column: 1,
      //   labelStyle: {
      //     width: '180px',
      //   },
      // }) as SModalDesProps;

      const descData = ref({});
      const getTitle = ref('预览');
      const sModalInstance: SModalDesInstance = {
        openModal,
        init,
      };
      instance && emit('register', sModalInstance, instance.uid);
      function init(descriptionProps: SModalDesProps) {
        [registerDesc, descMethod] = useDescription({
          ...Object.assign(propsConfig, descriptionProps),
          data: descData,
        });
      }
      function openModal(data: OpenModalDesData) {
        visible.value = true;
        const { record, title } = data;
        if (title) getTitle.value = title;
        descData.value = record || {};
      }
      function closeModal() {
        visible.value = false;
        emit('onClose', propsConfig);
      }

      return {
        visible,
        registerDesc,
        descMethod,
        closeModal,
        getTitle,
        propsConfig,
      };
    },
  });
</script>

<style lang="less" scoped>
  :deep(.ant-descriptions-view > table) {
    table-layout: auto;
  }
</style>
