<template>
  <Modal
    :title="getTitle"
    v-model:open="visible"
    v-bind="$attrs"
    :destroyOnClose="propsConfig.destroyOnClose"
    centered
    width="600px"
    :closable="propsConfig.showCloseButton"
  >
    <div class="px-8 p-t-8 p-b-4">
      <slot name="before"></slot>
      <BasicForm @register="registerForm" @submit="onSubmit" />
      <slot name="after"></slot>
    </div>
    <template #footer>
      <a-button
        v-if="propsConfig.showCloseButton"
        preIcon="ant-design:arrow-left-outlined"
        :iconSize="16"
        @click="closeModal()"
      >
        {{ propsConfig.backButtonText }}
      </a-button>
      <a-button
        preIcon="ant-design:check-outlined"
        :iconSize="16"
        :loading="loading"
        type="primary"
        @click="handlerSubmit()"
      >
        {{ propsConfig.saveButtonText }}
      </a-button>
    </template>
  </Modal>
</template>

<script lang="ts">
  import {
    defineComponent,
    ref,
    reactive,
    getCurrentInstance,
    nextTick,
    unref,
    computed,
  } from 'vue';
  import { isEmpty, isObject, isFunction, cloneDeep, isUndefined, omitBy } from 'lodash-es';
  import { Modal, message } from 'ant-design-vue';
  import { BasicForm, useForm } from '@/components/Form/index';
  import { SModalFormInstance, SModalFormProps, OpenModalFormData } from '../types/typing';
  import { useI18n } from '@/hooks/web/useI18n';

  export default defineComponent({
    name: 'SModalForm',
    components: { BasicForm, Modal },
    emits: ['register', 'success'],
    setup(_props: any, { emit }) {
      const { t } = useI18n();
      const instance = getCurrentInstance();
      let visible = ref<boolean>(false);
      const loading = ref<boolean>(false);
      const isUpdate = ref(false);
      let registerForm, formMethod, addFn, updateFn, callbackFn, merge;
      const propsConfig = reactive({
        autoFocusFirstItem: false,
        rowProps: { gutter: 20 },
        layout: 'vertical',
        baseColProps: { span: 24 },
        showActionButtonGroup: false,
        labelAlign: 'left',
        addText: t('component.s_modal_form.title_add'),
        updateText: t('component.s_modal_form.title_edit'),
        saveButtonText: t('component.s_modal_form.btn_save'),
        backButtonText: t('component.s_modal_form.btn_cancel'),
        destroyOnClose: false,
        showCloseButton: true,
      }) as SModalFormProps;
      const getTitle = computed(() => {
        return unref(isUpdate) ? propsConfig.updateText : propsConfig.addText;
      });

      const sModalInstance: SModalFormInstance = {
        openModal,
        init,
      };
      instance && emit('register', sModalInstance, instance.uid);
      function init(propsForm: SModalFormProps) {
        addFn = propsForm.addFn;
        updateFn = propsForm.updateFn;
        callbackFn = propsForm.callbackFn;
        if (propsForm) {
          Object.assign(propsConfig, propsForm);
          [registerForm, formMethod] = useForm(propsConfig);
        }
      }
      function openModal(data: OpenModalFormData) {
        visible.value = true;
        const { isUpdate: _isUpdate, record: _record, beforeFn, title } = data;
        merge = data.merge;
        isUpdate.value = _isUpdate;
        if (title && typeof title === 'string' && _isUpdate) {
          propsConfig.updateText = title;
        } else if (title && typeof title === 'string' && !_isUpdate) {
          propsConfig.addText = title;
        }
        nextTick(async () => {
          formMethod.resetFields();
          beforeFn && (await beforeFn(formMethod, data));
          if (!isEmpty(_record)) {
            formMethod.setFieldsValue(_record);
          }
        });
      }
      function closeModal() {
        visible.value = false;
      }
      function handlerSubmit() {
        if (formMethod?.submit) {
          formMethod?.submit();
        } else {
          message.error('fromMethod.submit 不存在于 SDrawerFrom 情况联系开发者解决异常');
        }
      }
      async function onSubmit(value) {
        let _result = {};
        try {
          loading.value = true;
          let result = cloneDeep(value);
          if (!isEmpty(merge) && isObject(merge)) {
            Object.assign(result, merge);
          } else if (isFunction(merge)) {
            result = merge(result) || {};
          }
          result = omitBy(result, isUndefined);
          _result = result;
          // console.log(result);
          if (unref(isUpdate)) {
            await updateFn(result);
          } else {
            await addFn(result);
          }
          callbackFn && callbackFn({ isUpdate: unref(isUpdate), status: true, result });
          closeModal();
          emit('success', { isUpdate: unref(isUpdate), status: true, result });
        } catch (error) {
          console.error(error);
          callbackFn && callbackFn({ isUpdate: unref(isUpdate), status: false, result: _result });
        } finally {
          loading.value = false;
        }
      }
      return {
        visible,
        registerForm,
        formMethod,
        loading,
        closeModal,
        getTitle,
        propsConfig,
        handlerSubmit,
        onSubmit,
      };
    },
  });
</script>

<style lang="less" scoped></style>
