<template>
  <ul class="flex flex-col gap-2 p-2 pt-4">
    <li
      v-for="item in props.list"
      :key="item.code"
      :class="[activeTabCodeRef === item.code ? 'bg-#E4F1FF1' : 'hover:bg-gray-100']"
      @click="activeTabCodeRef = item.code"
      class="cursor-pointer relative px-4 py-2 rounded-md transition-colors flex items-center gap-2"
      tabindex="0"
    >
      <motion.div
        v-if="activeTabCodeRef === item.code"
        layout
        layout-id="active-tab-bg"
        class="absolute inset-x-0 z-1 inset-y-0 bg-#E4F1FF rounded-md"
      />

      <span class="relative z-2" :class="getIcon(item.code)"></span>
      <span class="relative z-2">{{ item.name }}</span>
      <span class="relative z-2">({{ item.count }})</span>
    </li>
  </ul>
</template>

<script lang="ts" setup>
  import type { IStatistics } from '@/api/operation/audit';
  import { watchEffect } from 'vue';
  import { motion } from 'motion-v';

  interface Props {
    list?: IStatistics[];
  }

  const props = defineProps<Props>();

  const activeTabCodeRef = defineModel<string>('type', { required: false, default: undefined });

  const getIcon = (code: string) => {
    switch (code) {
      case 'education':
        return 'i-carbon-education';
      case 'apartment':
        return 'i-carbon-building';
      case 'employment':
        return 'i-carbon-job-daemon';
      case 'member':
        return 'i-carbon-user-identification';
      case 'store':
        return 'i-carbon-store';
      default:
        return 'i-carbon-notification';
    }
  };

  watchEffect(() => {
    const list = props.list;
    if (list) {
      const [first] = list;
      if (!activeTabCodeRef.value) activeTabCodeRef.value = first.code;
    }
  });
</script>
