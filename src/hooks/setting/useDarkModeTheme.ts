import { computed } from 'vue';
import { theme } from 'ant-design-vue';
import { useRootSetting } from '@/hooks/setting/useRootSetting';
import { ThemeEnum } from '@/enums/appEnum';

export function useDarkModeTheme() {
  const { getDarkMode } = useRootSetting();
  const { darkAlgorithm } = theme;
  const isDark = computed(() => getDarkMode.value === ThemeEnum.DARK);
  const darkTheme = {
    algorithm: [darkAlgorithm],
    token: {
      colorTextBase: '#c9d1d9',
      colorBorder: '#303030',
      colorPrimary: '#0960bd',
      borderRadius: 2,
    },
  };
  return {
    isDark,
    darkTheme,
  };
}
