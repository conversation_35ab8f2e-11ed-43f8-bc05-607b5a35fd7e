import { type Ref, ref, unref, watch } from 'vue';
import { AxiosProgressEvent } from 'axios';
import { sleep } from '@/utils/other';

interface UseApiLoadingReturn<T = any> {
  loading: Ref<boolean>;
  reload: (_params?: any) => Promise<T>;
  apiResult: Ref<T | undefined>;
  progress: Ref<number>;
}

export function useApiLoading<T = any>({
  api,
  params,
  immediate = true,
  watchParams = true,
  type = 'fetch',
}: {
  api: (...arg: any[]) => Promise<T>;
  params?: Ref<any> | Recordable;
  immediate?: boolean;
  watchParams?: boolean;
  type?: 'fetch' | 'file';
}): UseApiLoadingReturn<T> {
  const loading = ref(false);
  const apiResult = ref<T | undefined>();

  const progress = ref(0);

  const onUploadProgress = (progressEvent: AxiosProgressEvent) => {
    console.log('progressEvent', progressEvent);
    if (progressEvent.total) {
      progress.value = Math.floor((progressEvent.loaded / progressEvent.total) * 100);
    } else {
      progress.value = 0;
    }
  };

  const reload = async (_params?: any): Promise<T> => {
    try {
      loading.value = true;
      const queryParams = _params || unref(params);
      if (type === 'fetch') {
        apiResult.value = await (queryParams ? api(queryParams) : api());
      } else if (type === 'file') {
        if (!queryParams) {
          return Promise.reject(new Error('params is required'));
        }
        apiResult.value = await api(queryParams, {
          onUploadProgress,
        });
        progress.value = 100;
        await sleep(500); // 等待进度条消失
      }

      return Promise.resolve(apiResult.value) as T;
    } catch (e) {
      return Promise.reject(e);
    } finally {
      loading.value = false;
      progress.value = 0;
    }
  };

  if (watchParams && params) {
    watch(
      () => params,
      () => {
        reload();
      },
    );
  }
  immediate && reload();

  return {
    progress,
    loading,
    reload,
    apiResult,
  };
}
