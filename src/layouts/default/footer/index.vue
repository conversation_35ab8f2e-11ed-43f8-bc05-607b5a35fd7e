<template>
  <Layout.Footer :class="prefixCls" v-if="getShowLayoutFooter" ref="footerRef">
    <div :class="`${prefixCls}__links`">
      <a @click="openWindow(IPC_URL)"> 苏ICP备2025155202号 </a>
      <!-- <a>Copyright &copy;2025 江苏省通州湾江海联动开发示范区管理委员会</a> -->
    </div>
  </Layout.Footer>
</template>
<script lang="ts" setup>
  import { computed, unref, ref } from 'vue';
  import { Layout } from 'ant-design-vue';

  import { IPC_URL } from '@/settings/siteSetting';
  import { openWindow } from '@/utils';

  import { useRootSetting } from '@/hooks/setting/useRootSetting';
  import { useRouter } from 'vue-router';
  import { useDesign } from '@/hooks/web/useDesign';
  import { useLayoutHeight } from '../content/useContentViewHeight';

  defineOptions({ name: 'LayoutFooter' });

  const { getShowFooter } = useRootSetting();
  const { currentRoute } = useRouter();
  const { prefixCls } = useDesign('layout-footer');

  const footerRef = ref<ComponentRef>(null);
  const { setFooterHeight } = useLayoutHeight();

  const getShowLayoutFooter = computed(() => {
    if (unref(getShowFooter)) {
      const footerEl = unref(footerRef)?.$el;
      setFooterHeight(footerEl?.offsetHeight || 0);
    } else {
      setFooterHeight(0);
    }
    return unref(getShowFooter) && !unref(currentRoute).meta?.hiddenFooter;
  });
</script>
<style lang="less" scoped>
  @prefix-cls: ~'@{namespace}-layout-footer';

  @normal-color: rgba(155, 105, 105, 1);

  @hover-color: rgba(0, 0, 0, 0.85);

  .@{prefix-cls} {
    display: flex;
    flex-direction: column;
    justify-content: center;
    // 页脚固定高度
    height: 24px;
    color: @normal-color;
    font-size: 14px;
    line-height: 24px;
    text-align: center;
    gap: 4px;

    &__links {
      display: flex;
      justify-content: center;
      gap: 8px;

      a {
        color: @normal-color;

        &:hover {
          color: @hover-color;
        }
      }
    }
  }
</style>
