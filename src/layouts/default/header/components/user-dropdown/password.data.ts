import { FormSchema } from '@/components/Form/index';
import { useI18n } from '@/hooks/web/useI18n';

const { t } = useI18n();
function isPasswordSecure(password) {
  // 检查是否包含连续三个相邻的数字
  for (let i = 0; i < password.length - 2; i++) {
    if (
      /[0-9]/.test(password[i]) &&
      password[i + 1] - password[i] === 1 &&
      password[i + 2] - password[i + 1] === 1
    ) {
      return false;
    }
  }

  // 检查是否包含连续三个相邻的字母
  for (let i = 0; i < password.length - 2; i++) {
    if (
      /[a-zA-Z]/.test(password[i]) &&
      password.charCodeAt(i + 1) === password.charCodeAt(i) + 1 &&
      password.charCodeAt(i + 2) === password.charCodeAt(i) + 2
    ) {
      return false;
    }
  }

  // 密码安全
  return true;
}

export const passwordSchema: FormSchema[] = [
  {
    field: 'oldPassword',
    label: t('sys.user_dropdown.frm_password_oldPassword'),
    component: 'InputPassword',
    required: true,
    componentProps: {
      placeholder: t('sys.user_dropdown.frm_password_oldPassword_placeholder'),
      autocomplete: 'off',
    },
  },
  {
    field: 'newPassword',
    label: t('sys.user_dropdown.frm_password_password'),
    component: 'InputPasswordPro',
    required: true,
    rules: [
      {
        required: true,
        message: t('sys.user_dropdown.frm_password_password_placeholder'),
      },
      {
        min: 6,
        message: t('sys.user_dropdown.rule_password_min'),
      },
      {
        max: 20,
        message: t('sys.user_dropdown.rule_password_max'),
      },
      {
        // 不能有空格
        pattern: /^\S*$/,
        message: t('sys.user_dropdown.rule_password_noSpace'),
      },
      {
        // 不能有中文
        pattern: /^[^\u4e00-\u9fa5]*$/,
        message: t('sys.user_dropdown.rule_password_noChinese'),
      },
      {
        // 方法自定义校验规则
        validator: (_rule, value) => {
          // 定义一个正则
          const reg = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[^\da-zA-Z]).*$/;
          if (!reg.test(value)) {
            return Promise.reject(t('sys.user_dropdown.rule_password_complexity'));
          }
          return Promise.resolve();
        },
      },
      {
        validator: (_rule, value) => {
          const reg = /^(?!.*(.)\1{2})/g;
          if (!reg.test(value)) {
            return Promise.reject(t('sys.user_dropdown.rule_password_noRepeat'));
          }
          return Promise.resolve();
        },
      },
      {
        validator: (_rule, value) => {
          if (!isPasswordSecure(value)) {
            return Promise.reject(t('sys.user_dropdown.rule_password_noConsecutive'));
          }
          return Promise.resolve();
        },
      },
    ],
    componentProps: ({ formActionType, formModel }) => {
      return {
        placeholder: t('sys.user_dropdown.frm_password_password_placeholder'),
        autocomplete: 'off',
        onChange: () => {
          if (formModel.newpassword2) {
            formActionType.validateFields(['newpassword2']);
          }
        },
      };
    },
  },
  {
    field: 'newpassword2',
    label: t('sys.user_dropdown.frm_password_password1'),
    required: true,
    dynamicRules: ({ model }) => {
      return [
        {
          validator: (_rule, value) => {
            if (!value) {
              return Promise.resolve();
            }
            if (value !== model.newPassword) {
              return Promise.reject(t('sys.user_dropdown.rule_password_match'));
            }
            return Promise.resolve();
          },
        },
      ];
    },
    component: 'InputPassword',
    componentProps: {
      placeholder: t('sys.user_dropdown.frm_password_password1_placeholder'),
      autocomplete: 'off',
    },
  },
];
