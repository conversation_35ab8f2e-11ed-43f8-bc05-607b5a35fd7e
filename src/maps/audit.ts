import { createMap } from '@/utils/other';
import { CreateMapParams } from '#/utils';

export const AuditTypeList: CreateMapParams[] = [
  ['education', '人才补贴', '#FFB000'],
  ['apartment', '人才公寓', '#26C7B7'],
  ['employment', '就业补贴', '#1E90FF'],
  ['store', '商家认证', '#FF0000'],
  ['member', '信息认证', '#6932C9'],
];

export const AuditTypeArray = createMap(AuditTypeList);

// 状态;stage=暂存 init=待审核，approve=同意，refuse=拒绝
export const AuditStatusList: CreateMapParams[] = [
  ['stage', '暂存', '#FFB000'],
  ['init', '待审核', '#1E90FF'],
  ['approve', '审核通过', '#26C7B7'],
  ['refuse', '审核拒绝', '#FF0000'],
];

export const AuditStatusArray = createMap(AuditStatusList);

// 用户可选状态
export const AuditUserStatusList: CreateMapParams[] = [
  ['approve', '通过', '#26C7B7'],
  ['refuse', '拒绝', '#FF0000'],
];

export const AuditUserStatusArray = createMap(AuditUserStatusList);

// 申请方式 1=个人 2=企业

export const AuditApplyTypeList: CreateMapParams[] = [
  [1, '个人', '#1E90FF'],
  [2, '企业', '#FF0000'],
];

export const AuditApplyTypeArray = createMap(AuditApplyTypeList);
