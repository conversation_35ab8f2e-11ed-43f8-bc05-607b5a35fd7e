import { createMap } from '@/utils/other';
import { CreateMapParams } from '#/utils';

export const MenuTypeList: CreateMapParams[] = [
  ['0', '菜单', '#FFB000'],
  ['5', '页面', '#26C7B7'],
  ['4', '详情', '#A57AFA'],
  ['3', '内嵌', '#FF4C52'],
  ['1', '按钮', '#1F90FF'],
];

export const MenuTypeArray = createMap(MenuTypeList);

export const CouponState: CreateMapParams[] = [
  ['NORMAL', '正常', '#1F90FF'],
  ['TAKEN_DOWN', '下架', '#FFB000'],
  ['EXPIRED', '已过期', '#FF4C52'],
];

export const CouponStateArray = createMap(CouponState);

// 用户券状态
// ALL
// AWAIT
// INIT
// USE
// OVERDUE

export const UserCouponState: CreateMapParams[] = [
  ['ALL', '全部', '#1F90FF'],
  ['AWAIT', '待领取', '#26C7B7'],
  ['INIT', '已领取', '#A57AFA'],
  ['USE', '已使用', '#FF4C52'],
  ['OVERDUE', '已过期', '#FFB000'],
];

export const UserCouponStateArray = createMap(UserCouponState);

// 分类编码,可用值:YD,YG,YS,YDU

export const CouponCodeState: CreateMapParams[] = [
  ['YD', '悦动蓝湾', '#1F90FF'],
  ['YG', '悦购蓝湾', '#26C7B7'],
  ['YS', '悦宿蓝湾', '#A57AFA'],
  ['YDU', '悦读蓝湾', '#FF4C52'],
  ['LWYX', '蓝湾夜校', '#FFB000'],
];

export const CouponCodeStateArray = createMap(CouponCodeState);

// 状态;是否使用,可用值:ALL,AWAIT,INIT,USE,OVERDUE
export const CouponUseState: CreateMapParams[] = [
  ['ALL', '全部', '#1F90FF'],
  ['AWAIT', '待领取', '#26C7B7'],
  ['INIT', '已领取', '#A57AFA'],
  ['USE', '已使用', '#FF4C52'],
  ['OVERDUE', '已过期', '#FFB000'],
];

export const CouponUseStateArray = createMap(CouponUseState);
