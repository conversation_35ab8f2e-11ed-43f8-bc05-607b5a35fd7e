import { createMap } from '@/utils/other';
import { CreateMapParams } from '#/utils';

export const StepActionList: CreateMapParams[] = [
  [0, '下一步', '#FFB000', {}],
  [1, '提交', '#26C7B7'],
  [2, '完成', '#A57AFA'],
];

export const StepActionArray = createMap(StepActionList);

// 学历 1=大专 2=本科 3=硕士 4=博士

export const EducationList: CreateMapParams[] = [
  [1, '大专', '#FFB000'],
  [2, '本科', '#26C7B7'],
  [3, '硕士', '#A57AFA'],
  [4, '博士', '#FF5252'],
];

export const EducationArray = createMap(EducationList);

export const MemberSexList: CreateMapParams[] = [
  [1, '男', '#26C7B7'],
  [2, '女', '#FF5252'],
];
export const MemberSexArray = createMap(MemberSexList);

// talentType
// integer <int32>
// 可选
// 人才类别 1=A类 2=B类 3=C类 4=D类 5=E类 6=F类

export const TalentTypeList: CreateMapParams[] = [
  [1, 'A类', '#26C7B7'],
  [2, 'B类', '#FFB000'],
  [3, 'C类', '#A57AFA'],
  [4, 'D类', '#FF5252'],
  [5, 'E类', '#FF5252'],
  [6, 'F类', '#FF5252'],
];

export const TalentTypeArray = createMap(TalentTypeList);

// 婚姻状态改
export const MaritalStatusList: CreateMapParams[] = [
  [1, '未婚', '#26C7B7'],
  [2, '已婚', '#FFB000'],
];
export const MaritalStatusArray = createMap(MaritalStatusList);

// 教育类型 1=全日制教育 2=在职教育

export const EducationTypeList: CreateMapParams[] = [
  [1, '全日制教育', '#26C7B7'],
  [2, '在职教育', '#FFB000'],
];
export const EducationTypeArray = createMap(EducationTypeList);
//
