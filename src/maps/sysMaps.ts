import { createMap } from '@/utils/other';
import { CreateMapParams } from '#/utils';

export const MenuTypeList: CreateMapParams[] = [
  ['0', '菜单', '#FFB000'],
  ['5', '页面', '#26C7B7'],
  ['4', '详情', '#A57AFA'],
  ['3', '内嵌', '#FF4C52'],
  ['1', '按钮', '#1F90FF'],
];

export const MenuTypeArray = createMap(MenuTypeList);

export const StatusList: CreateMapParams[] = [
  [1, '启用', '#26C7B7'],
  [0, '禁用', '#FF4C52'],
];
export const StatusArray = createMap(StatusList);

export const StatusStoreList: CreateMapParams[] = [
  [true, '正常', '#26C7B7'],
  [false, '已下架', '#FF4C52'],
];

export const StatusStoreArray = createMap(StatusStoreList);
