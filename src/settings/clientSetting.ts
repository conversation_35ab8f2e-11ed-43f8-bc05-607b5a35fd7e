import { find } from 'lodash-es';
import { getUserInfo } from '@/api/sys/user';
import { getClientId } from '@/utils/auth';
import { PageEnum } from '@/enums/pageEnum';

interface Client {
  clientId: string;
  clientSecret: string;
  clientName: string;
  label: string;
  value: string;
  scope: string;
  apiUserInfo: () => Promise<any>;
  // apiPassWord: (data: any) => Promise<any>;
  homePath: string;
  // apiUserInfoExtend?: (params: any) => Promise<any>;
  // 是否允许切换区域
  allowSwitchArea: boolean;
}

interface ClientStore extends Client {
  Authorization: string;
}

export const clientList: Client[] = [
  {
    clientId: 'genius',
    clientSecret: 'genius',
    clientName: '平台管理者',
    label: '平台管理者',
    value: 'genius',
    scope: 'server',
    apiUserInfo: getUserInfo,
    homePath: PageEnum.BASE_HOME,
    allowSwitchArea: true,
  },
  {
    clientId: 'area_resource',
    clientSecret: 'area_resource',
    clientName: '区域合作伙伴',
    label: '区域合作伙伴',
    value: 'area_resource',
    scope: 'server',
    apiUserInfo: getUserInfo,
    homePath: PageEnum.BASE_HOME,
    allowSwitchArea: false,
  },
  {
    clientId: 'area_operation',
    clientSecret: 'area_operation',
    clientName: '区域运营商',
    label: '区域运营商',
    value: 'area_operation',
    scope: 'server',
    apiUserInfo: getUserInfo,
    homePath: PageEnum.BASE_HOME,
    allowSwitchArea: false,
  },
];

export function getClient(
  clientId: string | null | unknown = getClientId(),
): ClientStore | undefined {
  const foundClient = find(clientList, { clientId }) as Client;
  if (!foundClient) return undefined;
  const Authorization =
    'Basic ' + window.btoa(`${foundClient.clientId}:${foundClient.clientSecret}`);

  return {
    ...foundClient,
    Authorization,
  };
}
