import { uuid } from '@/utils/uuid';
import { createVNode, render } from 'vue';

type Props = {
  title?: string;
  close?: () => void;
  el?: HTMLElement;
  options?: { [key: string]: any };
  [key: string]: any;
};

type CreateFuncCompReturn = {
  close: () => void;
  render: any;
};

type CreateFuncComp = (option?: Props) => CreateFuncCompReturn;

// 创建一个函数组件
function createFuncComp(Components: any): CreateFuncComp {
  return function (option: Props = {}): { close: () => void; render: any } {
    const name = Components.name || Components.__name || 'other';
    const uuidStr = `${uuid('xxxxxxxxxxxxxxxxxxxx')}-${name}`;
    const div = document.createElement('div');
    div.setAttribute('class', uuidStr);
    document.body.appendChild(div);
    option.el = div;
    const close = function () {
      if (!option.el) return;
      render(null, div);
      document.body.removeChild(div);
      option = {};
    };
    option.close = close;
    render(createVNode(Components, option), div);
    return { close, render };
  };
}

export { createFuncComp };

export type { Props };
