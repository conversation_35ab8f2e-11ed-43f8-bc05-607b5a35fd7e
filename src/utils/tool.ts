import { isString, find } from 'lodash-es';
import dayjs from 'dayjs';
import { CreateMapReturn, ExtendCreateMapReturnValue } from '#/utils';

export function tryToArray(field: string | string[]): string[] | undefined {
  if (isString(field)) {
    const pattern = /^\[(.+)\]$/;
    if (pattern.test(field)) {
      const match = field.match(pattern);
      if (match && match[1]) {
        const keys = match[1].split(',');
        if (!keys.length) {
          return undefined;
        }
        return keys;
      }
    } else {
      return [field];
    }
  } else {
    return field;
  }
}

// 判断时间是否跨越到第二天 并返回有效日期
export function getEffectiveDate(startTime: string, endTime: string): string {
  // 获取当前日期
  const today = dayjs().format('YYYY-MM-DD');

  // 格式化开始时间和结束时间
  const start = dayjs(`${today} ${startTime}`);
  const end = dayjs(`${today} ${endTime}`);

  // 判断是否跨越到第二天
  if (end.isBefore(start)) {
    // 跨越到第二天时，返回第二天日期
    return dayjs().add(1, 'day').format('YYYY-MM-DD');
  } else {
    // 时间相等或不跨越时返回当天日期
    return today;
  }
}

// FinalizationRegistry 用于监控对象的垃圾回收
export const finalizationRegistry = new FinalizationRegistry((heldValue) => {
  console.log(`对象被垃圾回收: ${heldValue}`);
});

// 匹配 map 配合 createMap 使用
export function matchMap(
  value: string | number | undefined,
  listArr: CreateMapReturn,
): ExtendCreateMapReturnValue {
  const item = find(listArr, { value });
  if (!item) {
    return {
      value: '',
      label: '未知',
      color: '',
      extend: {},
      success: false,
    };
  }
  return {
    ...item,
    success: true,
  };
}
