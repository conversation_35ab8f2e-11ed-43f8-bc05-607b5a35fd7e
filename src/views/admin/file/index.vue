<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { fileColumns } from './data';
  import { apiFilePage, apiFileDelete } from '@/api/sys/file';
  import { get } from 'lodash-es';

  const [registerTable, { reload }] = useTable({
    columns: fileColumns,
    api: apiFilePage,
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const methods = {
    async del(record) {
      const id = get(record, 'id');
      if (!id) {
        return;
      }
      await apiFileDelete([id]);
      reload();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        icon: 'ant-design:delete-outlined',
        tooltip: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除吗？',
          confirm: methods.del.bind(null, record),
        },
      },
    ];
  }
</script>

<template>
  <page-wrapper title="文件管理">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
  </page-wrapper>
</template>

<style lang="less" scoped></style>
