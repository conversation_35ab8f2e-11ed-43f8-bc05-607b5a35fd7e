import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import Icon from '@/components/Icon/Icon.vue';
import { getMenuList } from '@/api/sys/menu';
import { get, isEmpty } from 'lodash-es';
import { showToTag } from '@/components/RenderVnode';
import { MenuTypeArray } from '@/maps/sysMaps';

export function columns(): BasicColumn[] {
  return [
    {
      title: '图标',
      dataIndex: 'icon',
      width: 120,
      customRender: ({ record }) => {
        if (get(record, 'meta.icon')) {
          return h(Icon, { icon: get(record, 'meta.icon') });
        }
      },
    },
    {
      title: '菜单名称',
      dataIndex: 'name',
    },

    {
      title: '组件地址',
      dataIndex: 'path',
    },
    {
      title: '权限标识',
      dataIndex: 'permission',
    },

    {
      title: '菜单类型',
      dataIndex: 'menuType',
      customRender: ({ text }) => {
        return showToTag({ text, arr: MenuTypeArray });
      },
    },
    {
      title: '排序',
      sorter: true,
      dataIndex: 'sortOrder',
      width: 100,
    },
  ];
}

export const menuFormSchema: FormSchema[] = [
  {
    field: 'menuType',
    label: '菜单类型',
    component: 'RadioButtonGroup',
    defaultValue: '0',
    componentProps: {
      options: MenuTypeArray,
    },
    required: true,
  },

  {
    field: 'parentId',
    label: '父级菜单',
    component: 'ApiTreeSelect',
    defaultValue: '-1',
    componentProps: {
      api: async () => {
        try {
          const tree = formartMenuData(await getMenuList());
          tree.unshift({ id: '-1', name: '更目录' });
          return Promise.resolve(tree);
        } catch (error) {
          return Promise.reject(error);
        }
      },
      valueField: 'id',
      labelField: 'name',
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'permission',
    label: '权限标识',
    component: 'Input',
  },
  {
    field: 'component',
    label: ({ model }) => {
      if (model.menuType === '3') {
        return '外链地址';
      }
      return '组件路径';
    },
    component: 'Input',
    required({ model }) {
      // 允许展示的类型
      const allowShowType = ['3', '4', '5'];
      return allowShowType.includes(model.menuType);
    },
    ifShow: ({ model }) => {
      return model.menuType !== '1';
    },
    componentProps: {
      maxlength: 100,
    },
  },
  {
    field: 'path',
    label: '路由',
    component: 'Input',
    required: true,
    ifShow: ({ model }) => {
      return model.menuType !== '1';
    },
    componentProps: {
      maxlength: 100,
    },
  },
  {
    field: 'icon',
    label: '菜单图标',
    component: 'IconPicker',
    show: ({ model }) => {
      return model.menuType !== '1';
    },
  },
  {
    field: 'sortOrder',
    label: '排序',
    component: 'InputNumber',
    defaultValue: 0,
    required: true,
    componentProps: {
      min: 0,
      step: 1,
      precision: 0,
    },
  },
  {
    field: 'id',
    component: 'Input',
    label: '菜单ID',
    show: false,
  },
];

// 可以显示的菜单类型
export const showMenuType = ['0', '5'];

function formartMenuData(data: any) {
  const res: any = [];
  if (!data) return res;
  data.forEach((item: any) => {
    let tmp: any = {};
    if (showMenuType.includes(item.menuType)) {
      tmp = item;
    }
    if (item.children) {
      const _res = formartMenuData(item.children);
      if (_res?.length > 0) {
        tmp.children = _res;
      }
    }
    if (!isEmpty(tmp)) {
      res.push(tmp);
    }
  });
  return res;
}
