<script lang="ts" setup>
  import { message } from 'ant-design-vue';
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { columns, menuFormSchema } from './data';
  import { getMenuList, editSysMenu, deleteSysMenu, addSysMenu } from '@/api/sys/menu';
  import { SModalForm, useSModalForm } from '@/components/SModal';
  import { get } from 'lodash-es';

  const [registerForm, { addModal, updateModal }] = useSModalForm({
    schemas: menuFormSchema,
    updateFn: editSysMenu,
    addFn: addSysMenu,
    destroyOnClose: true,
  });
  const [registerTable, { reload }] = useTable({
    api: getMenuList,
    columns: columns(),
    pagination: false,
    isTreeTable: true,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });
  function handleradd() {
    addModal();
  }
  const method = {
    // 更新
    update: (record: Recordable) => {
      updateModal({
        record: {
          ...record,
          icon: get(record, 'meta.icon'),
        },
      });
    },
    del: async (record: Recordable) => {
      try {
        let childrenLength = record?.children?.length || 0;
        if (childrenLength === 0) {
          await deleteSysMenu(record);
        } else {
          message.error('请先删除子菜单');
        }
      } catch (error) {
        return;
      } finally {
        reload();
      }
    },
    add: (record: Recordable) => {
      let childrenLength = record?.children?.length || 0;
      addModal({
        record: {
          sortOrder: childrenLength,
          parentId: record?.id,
          menuType: '5',
        },
      });
    },
  };

  // 可以被显示的新增按钮 menuType
  const showAddBtn = ['0', '5'];

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        icon: 'ant-design:plus-outlined',
        tooltip: '新增',
        onClick: method.add.bind(null, record),
        ifShow() {
          return record.menuType && showAddBtn.includes(record.menuType);
        },
      },
      {
        icon: 'ant-design:edit-outlined',
        tooltip: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        icon: 'ant-design:delete-outlined',
        tooltip: '删除',
        title: '确定删除吗？',
        color: 'error',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<template>
  <page-wrapper title="菜单管理">
    <template #extra>
      <a-button type="primary" @click="handleradd" preIcon="ant-design:plus-outlined">
        新增菜单
      </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SModalForm @register="registerForm" @success="reload" />
  </page-wrapper>
</template>

<style lang="less" scoped></style>
