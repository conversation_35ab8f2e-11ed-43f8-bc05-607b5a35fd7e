import { BasicColumn, FormSchema } from '@/components/Table';

export function columns(): BasicColumn[] {
  return [
    {
      title: '角色名称',
      dataIndex: 'roleName',
    },
    {
      title: '角色编码',
      dataIndex: 'roleCode',
    },
    {
      title: '备注',
      dataIndex: 'roleDesc',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 120,
    },
  ];
}

export const roleSchema: FormSchema[] = [
  {
    field: 'roleName',
    label: '角色名称',
    component: 'Input',
  },
  {
    field: 'roleCode',
    label: '角色编码',
    component: 'Input',
    dynamicDisabled: ({ model }) => {
      return !!model.id;
    },
  },
  {
    field: 'roleDesc',
    label: '备注',
    component: 'InputTextArea',
  },
  {
    field: 'id',
    label: '备注',
    component: 'Input',
    show: false,
  },
];
