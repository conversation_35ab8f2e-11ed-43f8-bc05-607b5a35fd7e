<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import {
    getRolePage,
    createRole,
    updateRole,
    deleteRole,
    putMenuByRole,
    getMenuByRole,
  } from '@/api/sys/role';
  import { columns, roleSchema } from './data';
  import { Modal, Tree, message, type TreeProps } from 'ant-design-vue';
  import { ref } from 'vue';
  import { getMenuList } from '@/api/sys/menu';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';

  const open = ref<boolean>(false);
  const [registerTable, { reload }] = useTable({
    api: getRolePage,
    rowKey: 'id',
    columns: columns(),
  });
  const checkedKeys = ref<any>();
  const selectedKeys = ref<string[]>();
  const treeData: any = ref([]);
  const roleId = ref('');
  const [registerForm, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: roleSchema,
    updateFn: updateRole,
    addFn: createRole,
  });

  const method = {
    add() {
      addDrawer({
        title: '新增角色',
      });
    },
    update(record: Recordable) {
      updateDrawer({ title: '编辑角色', record });
    },
    async del(record: Recordable) {
      try {
        await deleteRole(record);
      } catch (error) {
        return;
      } finally {
        reload();
      }
    },
    async permissions(record) {
      open.value = true;
      let data = await getMenuList();
      roleId.value = record.id;
      treeData.value = data;
      let Role = await getMenuByRole(record);
      if (Role.length > 0) {
        checkedKeys.value = Role.map((item) => item.id);
      }
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        icon: 'ant-design:edit-outlined',
        tooltip: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        icon: 'ant-design:link-outlined',
        tooltip: '配置权限',
        onClick: method.permissions.bind(null, record),
      },

      {
        icon: 'ant-design:delete-outlined',
        tooltip: '删除',
        title: '确定删除吗？',
        color: 'error',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
  const fieldNames: TreeProps['fieldNames'] = {
    key: 'id',
    title: 'name',
  };
  const handleOk = async () => {
    let data = await putMenuByRole({
      roleId: roleId.value,
      menuIds: checkedKeys.value.checked || checkedKeys.value,
    });

    if (data) {
      open.value = false;
      message.success('分配成功');
      reload();
    }
  };
</script>

<template>
  <page-wrapper title="角色管理">
    <template #extra>
      <a-button type="primary" @click="method.add"> 新增角色 </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :record="record" :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>

    <SDrawerForm @register="registerForm" @success="reload" />
    <Modal v-model:open="open" title="分配菜单" @ok="handleOk">
      <Tree
        v-model:checkedKeys="checkedKeys"
        :selectedKeys="selectedKeys"
        checkable
        checkStrictly
        :tree-data="treeData"
        :field-names="fieldNames"
      />
    </Modal>
  </page-wrapper>
</template>

<style lang="less" scoped></style>
