import { BasicColumn, FormSchema } from '@/components/Table';
import { showToBadge } from '@/components/RenderVnode';
import { StatusArray } from '@/maps/sysMaps';
import { getRolePage } from '@/api/sys/role';
import { getAllPageData } from '@/utils/other';
import { phoneReg } from '@/utils/validate';

export function columns(): BasicColumn[] {
  return [
    {
      title: '名称',
      dataIndex: 'name',
    },
    {
      title: '登录账号',
      dataIndex: 'username',
    },
    {
      title: '手机号',
      dataIndex: 'phone',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
    },
    {
      title: '启用状态',
      dataIndex: 'status',
      customRender: ({ text }) => {
        return showToBadge({ text, arr: StatusArray });
      },
    },
  ];
}

export const userFormSchema: FormSchema[] = [
  {
    field: 'username',
    label: '登录账号',
    required: true,
    component: 'Input',
    dynamicDisabled({ model }) {
      return model?.id ? true : false;
    },
  },
  {
    field: 'name',
    label: '名称',
    required: true,
    component: 'Input',
  },
  {
    field: 'roleIds',
    label: '角色',
    component: 'ApiSelect',
    componentProps: {
      api: () => getAllPageData(getRolePage),
      showSearch: true,
      valueField: 'id',
      labelField: 'roleName',
      // 多选
      mode: 'multiple',
    },
  },
  {
    field: 'phone',
    label: '手机号',
    component: 'Input',
    rules: [phoneReg],
  },
  {
    field: 'email',
    label: '邮箱',
    component: 'Input',
    rules: [{ type: 'email', message: '请输入正确的邮箱地址' }],
  },
  {
    field: 'status',
    label: '是否启用',
    component: 'RadioButtonGroup',
    componentProps: {
      options: StatusArray,
    },
    defaultValue: 1,
  },

  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const passwordSchema: FormSchema[] = [
  {
    field: 'password',
    label: '新密码',
    component: 'InputPassword',
    required: true,
  },
  {
    field: 'confirmPassword',
    label: '确认新密码',
    component: 'InputPassword',
    dynamicRules({ values }) {
      return [
        {
          required: true,
          validator: async (_rule, value) => {
            if (!value) {
              return Promise.reject('请确认新密码');
            }
            if (value !== values.password) {
              return Promise.reject('两次输入的密码不匹配!');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },
  {
    field: 'userId',
    component: 'Input',
    label: 'userId',
    show: false,
  },
];

// export const assignRoleSchema: FormSchema[] = [
//   {
//     field: 'roleIds',
//     label: '角色',
//     component: 'ApiSelect',
//     required: true,
//     componentProps: {
//       api: async () => {
//         try {
//           const res = await getAllPageData(getRolePage);
//           return res;
//         } catch (error) {
//           return [];
//         }
//       },
//       valueField: 'roleId',
//       labelField: 'roleName',
//     },
//   },
//   {
//     field: 'userId',
//     component: 'Input',
//     label: 'userId',
//     show: false,
//   },
// ];
