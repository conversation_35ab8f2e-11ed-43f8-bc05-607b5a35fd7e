<script lang="ts" setup>
  import { BasicTable, useTable, ActionItem, TableAction } from '@/components/Table';
  import { columns, userFormSchema, passwordSchema } from './data';
  import { getUserPage, createUser, editUser, deleteUser } from '@/api/sys/user';
  import { SModalForm, useSModalForm } from '@/components/SModal';
  import { SDrawerForm, useSDrawerForm } from '@/components/SDrawer';
  import { omit } from 'lodash-es';

  const [registerForm, { addDrawer, updateDrawer: updateModal }] = useSDrawerForm({
    schemas: userFormSchema,
    updateFn: editUser,
    addFn: createUser,
  });
  const [registerFormPassword, { updateModal: updateModalPassword }] = useSModalForm({
    schemas: passwordSchema,
    updateFn: editUser,
  });

  const [registerTable, { reload }] = useTable({
    api: getUserPage,
    columns: columns(),
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const method = {
    // 更新
    update: (record: Recordable) => {
      updateModal({
        record,
      });
    },
    del: async (record: Recordable) => {
      try {
        await deleteUser(record);
      } catch (error) {
        return;
      } finally {
        reload();
      }
    },
    // 新增
    add: () => {
      console.log('add');
      addDrawer({
        title: '新增用户',
      });
    },
    // 修改密码
    updatePassword: (record: Recordable) => {
      updateModalPassword({
        title: '修改密码',
        record: omit(record, ['password']),
        merge: (v) => {
          return { ...v, confirmPassword: undefined };
        },
      });
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        icon: 'ant-design:edit-outlined',
        tooltip: '编辑',
        onClick: method.update.bind(null, record),
      },
      {
        icon: 'ant-design:delete-outlined',
        tooltip: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
        ifShow: false,
      },
    ];
  }
</script>

<template>
  <page-wrapper title="账号管理">
    <template #extra>
      <a-button type="primary" @click="method.add" preIcon="ant-design:plus-outlined">
        新增用户
      </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerForm" @success="reload" />
    <SModalForm @register="registerFormPassword" @success="reload" />
  </page-wrapper>
</template>
