<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { apartmentUserColumns } from './data';
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { apiAuditPage } from '@/api/operation/audit';
  import { useGo } from '@/hooks/web/usePage';

  interface Props {
    targetId: string;
  }

  const props = defineProps<Props>();
  const go = useGo();
  const [registerTable] = useTable({
    columns: apartmentUserColumns,
    api: apiAuditPage,
    useSearchForm: false,
    searchInfo: {
      type: 'apartment',
      targetId: props.targetId,
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: () => {
          go(`/apartment/info/info-audit/${record.id}`);
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
