import { BasicColumn, FormSchema } from '@/components/Table';
import { DescItem } from '@/components/Description';
import { ApartmentConfigArray } from '@/maps/apartmentMap';
import { Tinymce } from '@/components/Tinymce';
import { h } from 'vue';
import { previewImage, showToTag, showToBadge, htmlRender } from '@/components/RenderVnode';
import { map, size } from 'lodash-es';
import { AuditStatusArray } from '@/maps/audit';

export const listColumns: BasicColumn[] = [
  {
    title: '公寓名称',
    dataIndex: 'name',
  },
  {
    title: '公寓地址',
    dataIndex: 'address',
  },
  {
    title: '申请通过人数',
    dataIndex: 'passCount',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '公寓名称',
    component: 'Input',
  },
];

export const memberSchema: FormSchema[] = [
  {
    field: 'thumbnail',
    label: '缩略图',
    component: 'UploadImage',
    required: true,
  },
  {
    field: 'photo',
    label: '主图',
    component: 'UploadImage',
    required: true,
  },
  {
    field: 'name',
    label: '公寓名称',
    required: true,
    component: 'Input',
  },
  {
    field: 'address',
    label: '公寓地址',
    required: true,
    component: 'Input',
    componentProps: {
      maxlength: 40,
    },
  },

  {
    field: 'modelList',
    label: '房型',
    required: true,
    defaultValue: [{ name: '' }],
    component: 'InputList',
    rules: [
      {
        validator: (rule, value) => {
          if (value.some((item) => item.name === '')) return Promise.reject('请输入房型');
          return Promise.resolve();
        },
      },
    ],
  },
  {
    field: 'typeConfigList',
    label: '房型配置',
    required: true,
    component: 'Select',
    componentProps: {
      mode: 'multiple',
      options: ApartmentConfigArray,
    },
  },
  {
    field: 'introduction',
    component: 'Input',
    label: '房源介绍',
    defaultValue: '',
    rules: [{ required: true }],
    render: ({ model, field }) => {
      return h(Tinymce, {
        plugins: ['wordcount image link fullscreen'],
        toolbar: [
          'fontsizeselect bold italic underline forecolor image link alignleft aligncenter alignright fullscreen',
        ],
        value: model[field],
        onChange: (value: string) => {
          model[field] = value;
        },
      });
    },
  },
  {
    field: 'describe',
    label: '申请须知',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 500,
      showCount: true,
    },
    required: true,
  },
  {
    field: 'promise',
    label: '签署承诺书',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      maxlength: 500,
      showCount: true,
    },
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const basicDesc: DescItem[] = [
  {
    field: 'photo',
    label: '主图',
    render: (val: string) => {
      return previewImage(val, 'desc');
    },
  },
  {
    field: 'modelList',
    label: '房型',
    render: (val: any) => {
      return map(val, (item) => item.name).join(';') || '--';
    },
  },
  {
    field: 'typeConfigList',
    label: '房型配置',
    render: (val) => {
      if (size(val) === 0) return '';

      const tags = map(val, (item) => {
        return showToTag({
          text: item,
          arr: ApartmentConfigArray,
        });
      });
      return h('div', {}, tags);
    },
  },
  {
    field: 'introduction',
    label: '房源介绍',
    render: (val) => {
      return htmlRender(val);
    },
  },
  {
    field: 'describe',
    label: '申请须知',
  },
  {
    field: 'promise',
    label: '签署承诺书',
  },
];

// 申请指南
export const applyGuideSchema: FormSchema[] = [
  {
    field: 'title',
    label: '标题',
    component: 'Input',
  },
  {
    field: 'type',
    label: '类型',
    component: 'Input',
    show: false,
  },
  {
    field: 'id',
    label: 'ID',
    component: 'Input',
    show: false,
  },
  {
    field: 'content',
    label: '内容',
    component: 'Input',
    defaultValue: '',
    rules: [{ required: true }],
    render: ({ model, field }) => {
      return h(Tinymce, {
        plugins: ['wordcount image link fullscreen'],
        toolbar: [
          'fontsizeselect bold italic underline forecolor image link alignleft aligncenter alignright fullscreen',
        ],
        value: model[field],
        onChange: (value: string) => {
          model[field] = value;
        },
      });
    },
  },
];

export const apartmentUserColumns: BasicColumn[] = [
  {
    title: '申请公寓',
    dataIndex: 'apartmentName',
  },
  {
    title: '申请户型',
    dataIndex: 'subTarget',
  },
  {
    title: '申请时间',
    dataIndex: 'submitTime',
  },
  {
    title: '审批状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      return showToBadge({ text, arr: AuditStatusArray });
    },
  },
  {
    title: '审批人',
    dataIndex: 'handleUserName',
  },
  {
    title: '审批时间',
    dataIndex: 'handlerTime',
  },
];
