<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { listColumns, searchSchema, memberSchema } from './data';
  import { useGo } from '@/hooks/web/usePage';
  import { PageEnum } from '@/enums/pageEnum';
  import {
    apiApartmentPage,
    apiApartmentAdd,
    apiApartmentEdit,
    apiApartmentDelete,
  } from '@/api/operation/apartment';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';

  const go = useGo();
  const [registerTable, { reload }] = useTable({
    columns: listColumns,
    api: apiApartmentPage,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerDrawer, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: memberSchema,
    addFn: apiApartmentAdd,
    updateFn: apiApartmentEdit,
  });

  const method = {
    goToInfo(record: Recordable) {
      go(`${PageEnum.APARTMENT_INFO}/${record.id}`);
    },
    add() {
      addDrawer();
    },
    edit(record: Recordable) {
      updateDrawer({
        record,
      });
    },
    async del(record: Recordable) {
      await apiApartmentDelete(record.id);
      reload();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: method.goToInfo.bind(null, record),
      },
      {
        label: '编辑',
        onClick: method.edit.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<template>
  <page-wrapper title="人才公寓管理">
    <template #extra>
      <a-button preIcon="ant-design:plus-outlined" type="primary" @click="method.add()"
        >新增公寓</a-button
      >
    </template>
    <div>
      <BasicTable @register="registerTable">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="tableAction(record)" />
          </template>
        </template>
      </BasicTable>
      <SDrawerForm @register="registerDrawer" @success="reload()" />
    </div>
  </page-wrapper>
</template>

<style lang="less" scoped>
  .test {
    height: 100%;
    padding-top: 20px;
    padding-right: 20px;
    padding-left: 20px;

    &-content {
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      background-color: red;
    }
  }
</style>
