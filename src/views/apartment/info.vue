<template>
  <page-wrapper v-loading="loading" @back="go(-1)" title="公寓详情">
    <a-row :gutter="[20, 20]" :wrap="false">
      <a-col flex="300px">
        <div class="apartment-info-thumbnail">
          <Image
            style="object-fit: cover"
            class="apartment-info-thumbnail-img"
            height="100%"
            width="100%"
            :src="thumbnail"
          />
        </div>
        <div class="apartment-info-title">
          <div>{{ get(data, 'name') }}</div>
          <div class="address">
            <Icon icon="material-symbols-light:location-on-outline" />
            {{ get(data, 'address') }}
          </div>
        </div>
      </a-col>
      <a-col flex="auto">
        <Tabs default-active-key="1">
          <Tabs.TabPane key="1" tab="基本信息">
            <a-card>
              <Description :column="1" :schema="basicDesc" :data="data" :bordered="false" />
            </a-card>
          </Tabs.TabPane>
          <Tabs.TabPane key="2" tab="申请记录">
            <apartmentUser :targetId="pageId" />
          </Tabs.TabPane>
        </Tabs>
      </a-col>
    </a-row>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { Image, Tabs } from 'ant-design-vue';
  import { Description } from '@/components/Description';
  import { computed } from 'vue';
  import { basicDesc } from './data';
  import { get } from 'lodash-es';
  import Icon from '@/components/Icon/Icon.vue';
  import { useRoute } from 'vue-router';
  import { apiApartmentInfo } from '@/api/operation/apartment';
  import { useGo } from '@/hooks/web/usePage';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import apartmentUser from './apartment-user.vue';

  const go = useGo();

  const route = useRoute();
  const pageId = computed(() => get(route, 'params.id') as string);
  const { loading, apiResult: data } = useApiLoading({
    api: apiApartmentInfo,
    params: route.params,
  });

  const thumbnail = computed(() => {
    return get(data.value, 'thumbnail', '');
  });
</script>

<style lang="less" scoped>
  .apartment-info-thumbnail {
    width: 100%;
    height: 120px;
    overflow: hidden;
    border-radius: 5px;
  }

  .apartment-info-title {
    display: flex;
    flex-direction: column;
    margin-top: 20px;
    font-size: 16px;
    font-weight: bold;
    gap: 10px;

    .address {
      color: #999;
      font-size: 12px;
      font-weight: normal;
    }
  }
</style>
