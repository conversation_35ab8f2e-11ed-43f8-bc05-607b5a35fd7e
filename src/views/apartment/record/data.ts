import { showToBadge, showToTag } from '@/components/RenderVnode';
import { BasicColumn, FormSchema } from '@/components/Table';
import { AuditStatusArray } from '@/maps/audit';
import { MemberSexArray, TalentTypeArray } from '@/maps/member';

export const basicColumns: BasicColumn[] = [
  {
    title: '申请人',
    dataIndex: 'submitBy',
  },
  {
    title: '联系电话',
    dataIndex: 'submitPhone',
  },
  {
    title: '性别',
    dataIndex: 'sex',
    customRender: ({ text }) => {
      return showToTag({ text, arr: MemberSexArray });
    },
  },

  {
    title: '人才类别',
    dataIndex: 'talentType',
    customRender: ({ text }) => {
      return showToTag({ text, arr: TalentTypeArray });
    },
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
  },
  {
    title: '户籍',
    dataIndex: 'nativePlace',
  },
  {
    title: '状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      return showToBadge({ text, arr: AuditStatusArray });
    },
  },
  {
    title: '申请公寓',
    dataIndex: 'apartmentName',
  },
  {
    title: '申请户型',
    dataIndex: 'subTarget',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '申请时间',
    dataIndex: 'submitTime',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'searchWord',
    label: '申请人',
    component: 'Input',
  },
  {
    field: '[startDate,endDate]',
    label: '申请时间',
    component: 'RangePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: {
        width: '100%',
      },
    },
  },
];
