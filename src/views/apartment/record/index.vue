<template>
  <page-wrapper title="申请记录">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { basicColumns, searchSchema } from './data';
  import { useGo } from '@/hooks/web/usePage';
  import { apiAuditPage } from '@/api/operation/audit';

  const go = useGo();
  const [registerTable] = useTable({
    columns: basicColumns,
    api: apiAuditPage,
    formConfig: {
      schemas: searchSchema,
    },
    useSearchForm: true,
    searchInfo: {
      type: 'apartment',
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: () => {
          go(`/apartment/info-audit/${record.id}`);
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
