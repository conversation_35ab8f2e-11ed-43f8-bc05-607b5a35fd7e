import { showToBadge, showToTag } from '@/components/RenderVnode';
import { BasicColumn, FormSchema } from '@/components/Table';
import { AuditTypeArray, AuditStatusArray, AuditUserStatusArray } from '@/maps/audit';
import { EducationArray } from '@/maps/member';
import { computed, ref } from 'vue';

type FilterableColumn = BasicColumn & {
  filter?: () => boolean;
};

export const currentFilterTypeRef = ref<string>();
const isStoreRef = computed(() => {
  return currentFilterTypeRef.value === 'store';
});

export const listColumnsRef = computed<FilterableColumn[]>(() =>
  [
    {
      title: '申请人',
      dataIndex: 'name',
    },
    {
      title: '联系电话',
      dataIndex: 'submitPhone',
      width: 140,
    },
    {
      title: '身份证号',
      dataIndex: 'idCard',
      width: 180,
      filter() {
        return !isStoreRef.value;
      },
    },
    {
      title: '学历',
      dataIndex: 'education',
      customRender(opt) {
        return showToTag({ text: opt.text, arr: EducationArray });
      },
      filter() {
        return !isStoreRef.value;
      },
    },
    {
      title: '公司名称',
      dataIndex: 'enterpriseName',
      filter() {
        return !isStoreRef.value;
      },
    },
    {
      title: '申请类型',
      dataIndex: 'type',
      customRender: ({ text }) => {
        return showToTag({ text, arr: AuditTypeArray });
      },
    },
    {
      title: '店铺地址',
      dataIndex: 'memberAddress',
      filter() {
        return isStoreRef.value;
      },
    },
    {
      title: '状态',
      dataIndex: 'state',
      customRender: ({ text }) => {
        return showToBadge({ text, arr: AuditStatusArray });
      },
    },
    {
      title: '创建人',
      dataIndex: 'createBy',
    },
    {
      title: '申请时间',
      dataIndex: 'submitTime',
      width: 200,
    },
  ].filter((item) => {
    return item.filter ? item.filter() : true;
  }),
);

export const searchSchema: FormSchema[] = [
  {
    field: 'searchWord',
    label: '申请人',
    component: 'Input',
  },
  {
    field: 'idCard',
    label: '身份证号',
    component: 'Input',
  },
  {
    field: 'submitPhone',
    label: '联系电话',
    component: 'Input',
  },
  {
    field: 'education',
    label: '学历',
    component: 'Select',
    componentProps: {
      options: EducationArray,
    },
  },
  {
    field: 'enterpriseName',
    label: '公司名称',
    component: 'Input',
  },
  // {
  //   field: 'type',
  //   label: '申请类型',
  //   component: 'Select',
  //   componentProps: {
  //     options: AuditTypeArray,
  //   },
  // },
  {
    field: '[startDate,endDate]',
    label: '申请时间',
    component: 'RangePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: {
        width: '100%',
      },
    },
  },
];

export const basiceSchema: FormSchema[] = [
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'state',
    component: 'RadioButtonGroup',
    label: '状态',
    defaultValue: 'approve',
    componentProps: {
      options: AuditUserStatusArray,
    },
    required: true,
  },
  {
    field: 'reason',
    component: 'InputTextAreaAudit',
    label: '备注',
  },
];
