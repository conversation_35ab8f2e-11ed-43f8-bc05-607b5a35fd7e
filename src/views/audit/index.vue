<template>
  <section class="bg-white mt-4 ml-4 flex flex-row w-[calc(100%-2rem)] [--aside-width:200px]">
    <aside class="w-$aside-width shrink-0 border-r-1 border-r-solid border-r-#F9F9FA">
      <TheAduitTabs :list="statisticsListRef" v-model:type="searchInfo.type" />
    </aside>
    <page-wrapper
      title="待办审核"
      class="pt-0! pl-0! [&_.ant-page-header]:pt-2! max-w-[calc(100%-var(--aside-width))]"
    >
      <template #extra>
        <a-button
          class="ml-auto"
          preIcon="material-symbols:download"
          type="primary"
          @click="handleExport"
          :loading="exportLoading"
        >
          导出
        </a-button>
      </template>

      <BasicTable @register="registerTable">
        <template #tableTitle>
          <div class="w-full flex flex-row pr">
            <RadioGroup v-model:value="searchInfo.state" button-style="solid">
              <RadioButton :value="1">待审核 ({{ get(apiResult, 'waitCount', 0) }})</RadioButton>
              <RadioButton :value="2">已审核 ({{ get(apiResult, 'passCount', 0) }})</RadioButton>
            </RadioGroup>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="tableAction(record)" />
          </template>
        </template>
      </BasicTable>
      <SModalForm @register="registerModal" @success="method.reload" />
    </page-wrapper>
  </section>
</template>

<script lang="ts" setup>
  import { RadioGroup, RadioButton, message } from 'ant-design-vue';
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { listColumnsRef, searchSchema, basiceSchema, currentFilterTypeRef } from './data';
  import {
    apiAuditPage,
    apiAuditHandle,
    apiAduitCollect,
    IStatistics,
    apiAduitStatistics,
    apiAuditExport,
  } from '@/api/operation/audit';
  import { watch, watchEffect } from 'vue';
  import { SModalForm, useSModalForm } from '@/components/SModal';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { get, pick } from 'lodash-es';
  import { useGo } from '@/hooks/web/usePage';
  import { downloadByUrl, downloadByData } from '@/utils/file/download';
  import TheAduitTabs from '@/components/TheAduitTabs/index.vue';
  import { to } from '@bryce-loskie/utils';
  import { useSessionStorage } from '@vueuse/core';

  const go = useGo();

  const searchInfo = useSessionStorage('audit-search-info', {
    state: 1,
    type: undefined,
  });

  watchEffect(() => {
    currentFilterTypeRef.value = searchInfo.value.type;
  });

  const { apiResult, reload: collectReload } = useApiLoading({
    api: apiAduitCollect,
    params: {
      type: searchInfo.value.type,
    },
    immediate: false,
  });

  watch(
    () => searchInfo.value.type,
    (type) => {
      collectReload({ type });
    },
    {
      immediate: true,
    },
  );

  const { apiResult: statisticsListRef, reload: statisticsReload } = useApiLoading<IStatistics[]>({
    api: apiAduitStatistics,
  });

  // export
  const { loading: exportLoading, reload: rawHandleExport } = useApiLoading({
    api: apiAuditExport,
    immediate: false,
  });

  const handleExport = async () => {
    const payload = getForm().getFieldsValue();
    const [err, res] = await to(rawHandleExport({ ...payload, ...searchInfo.value }));
    if (err) {
      message.error('导出失败, 请稍后重试');
      return;
    }

    const { headers, data } = res;
    const filename =
      headers['content-disposition']?.split("filename*=utf-8''")[1]?.split(';')?.[0] ||
      '待办审核.xlsx';
    const contentType =
      headers['content-type']?.split(';')?.[0] ||
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    downloadByData(data, filename, contentType);
  };

  const [registerTable, { reload, getForm }] = useTable({
    columns: listColumnsRef,
    api: apiAuditPage,
    immediate: true,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
    searchInfo,
    actionColumn: {
      width: 220,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerModal, { addModal }] = useSModalForm({
    schemas: basiceSchema,
    addFn: apiAuditHandle,
  });

  const method = {
    audit(record: any) {
      addModal({
        title: '审核',
        record: pick(record, ['id']),
      });
    },
    // 刷新
    reload() {
      reload();
      collectReload();
      statisticsReload();
    },
    info(record: any) {
      if (!record?.id) {
        message.error('缺少id');
        return;
      }
      const aduitType = get(record, 'type', '');

      switch (aduitType) {
        case 'store':
          go(`/audit/store-info/${record.id}`);
          break;
        case 'member':
          go(`/audit/member-info/${record.id}`);
          break;

        default:
          go(`/audit/info/${record.id}`);
          break;
      }
    },
    // 下载文件
    download(record: any) {
      console.log(record);
      downloadByUrl({
        url: record.enterpriseAttach,
      });
    },
  };

  // 需要审核的状态

  const tableAction = (record: Recordable): ActionItem[] => {
    return [
      {
        label: '详情',
        onClick: method.info.bind(null, record),
      },
      {
        label: '下载企业审核表',
        onClick: method.download.bind(null, record),
        ifShow: () => {
          return !!record?.enterpriseAttach;
        },
      },
      // {
      //   label: '审核',
      //   onClick: method.audit.bind(null, record),
      //   ifShow: () => record?.state === 'init',
      // },
    ];
  };
</script>
