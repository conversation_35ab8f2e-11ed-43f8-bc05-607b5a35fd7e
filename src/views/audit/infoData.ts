import { DescItem } from '@/components/Description';
import { showToTag, previewImage, fileRender } from '@/components/RenderVnode';
import {
  EducationArray,
  MemberSexArray,
  TalentTypeArray,
  MaritalStatusArray,
  EducationTypeArray,
} from '@/maps/member';
import { get } from 'lodash-es';

export const basicMemberDesc: DescItem[] = [
  {
    label: '2寸免冠照片',
    field: 'photo',
    render: (text) => {
      return previewImage(text, 'miniDesc');
    },
    span: 2,
  },
  {
    label: '姓名',
    field: 'name',
  },
  {
    label: '人才类别',
    field: 'talentType',
    render: (text) => {
      return showToTag({ text, arr: TalentTypeArray });
    },
  },
  {
    label: '性别',
    field: 'sex',
    render: (text) => {
      return showToTag({ text, arr: MemberSexArray });
    },
  },
  {
    label: '籍贯',
    field: 'nativePlace',
  },

  {
    label: '出生日期',
    field: 'birthday',
  },

  {
    label: '证件号',
    field: 'idCard',
  },
  {
    label: '出生地',
    field: 'memberAddress',
  },
  {
    label: '民族',
    field: 'nation',
  },
  {
    label: '本人电话',
    field: 'phone',
  },
  {
    label: '婚姻状态',
    field: 'maritalStatus',
    render: (text) => {
      return showToTag({ text, arr: MaritalStatusArray });
    },
  },
  {
    label: '身份证',
    field: '-',
    render: (_, record) => {
      // 将身份证正反面图片拼接成一个数组
      const idCardImages = [record.idCardFrontAttach, record.idCardBackAttach];
      // 过滤掉空值
      const validImages = idCardImages.filter((image) => image).join(',');
      return previewImage(validImages, 'desc');
    },
  },
];

export const educationMemberDesc: DescItem[] = [
  {
    label: '教育类型',
    field: 'educationType',
    render(text) {
      return showToTag({ text, arr: EducationTypeArray });
    },
  },
  {
    label: '入学时间',
    field: 'admissionDate',
  },
  {
    label: '毕业时间',
    field: 'graduationDate',
  },
  {
    label: '毕业院校',
    field: 'school',
  },
  {
    label: '所学专业',
    field: 'major',
  },
  {
    label: '学历',
    field: 'education',
    render(text) {
      return showToTag({ text, arr: EducationArray });
    },
  },
  {
    label: '学历证明',
    field: 'certificateAttach',
  },
];

export const enterpriseMemberDesc: DescItem[] = [
  {
    label: '参加工作时间',
    field: 'entryDate',
  },
  {
    label: '专业技术职称',
    field: 'technicalTitle',
  },
  {
    label: '现工作单位',
    field: 'enterpriseName',
  },

  {
    label: '单位联系人',
    field: 'enterpriseContact',
  },
  {
    label: '单位联系人电话',
    field: 'enterprisePhone',
  },
  {
    label: '职位',
    field: 'postType',
  },
  {
    label: '劳动合同签订期限',
    field: '-',
    render: (_, record) => {
      return `${get(record, 'laborContractTermStart')} 至 ${get(record, 'laborContractTermEnd')}}`;
    },
  },
  {
    label: '与单位签订的劳动合同',
    field: 'laborContract',
  },
  {
    label: '单位营业执照',
    field: 'businessLicense',
  },
];

// 申请信息
export const applyMemberDesc: DescItem[] = [
  {
    label: '申请租期（起止日期）',
    field: 'applyTerm',
  },
  {
    label: '申请公寓名称',
    field: 'apartmentName',
  },
  {
    label: '申请房型',
    field: 'subTarget',
  },
  {
    label: '申请时间',
    field: 'submitTime',
  },
  {
    label: '创建人',
    field: 'createBy',
  },
  {
    label: '企业审核表',
    field: 'enterpriseAttach',
  },
];

// 商家申请审核信息
export const basicBusinessDesc: DescItem[] = [
  {
    label: '店铺名称',
    field: 'expand.title',
    span: 2,
  },
  {
    label: '联系人',
    field: 'expand.linkman',
  },
  {
    label: '联系电话',
    field: 'expand.phone',
  },
  {
    label: '店铺地址',
    field: 'expand.address',
    span: 2,
  },
  {
    label: '门头照片',
    field: 'expand.photo',
    render: (text) => previewImage(text, 'miniDesc'),
  },
];

// 用户信息审核信息
export const basicUserDesc: DescItem[] = [
  {
    label: '用户姓名',
    field: 'name',
    span: 2,
  },
  {
    label: '身份证号码',
    field: 'idCard',
  },
  {
    label: '联系电话',
    field: 'memberPhone',
  },
  {
    label: '学历',
    field: 'education',
    render(text) {
      return showToTag({ text, arr: EducationArray });
    },
  },
  {
    label: '公司名称',
    field: 'enterpriseName',
  },
  {
    label: '学历证明',
    field: 'certificateAttach',
    render: (text) => fileRender(text),
  },
  {
    label: '审核备注信息',
    field: 'reasons',
    show: (record) => {
      return !!get(record, 'reasons');
    },
  },
];
