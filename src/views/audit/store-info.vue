<script lang="ts" setup>
  // 获取路由
  import { useRoute } from 'vue-router';
  import BasicUser from '@/components/Business/BasicUser.vue';
  import DividerTitle from '@/components/Form/src/extend/DividerTitle.vue';
  import { Description } from '@/components/Description';
  import { apiAduitDetail, apiAuditHandle } from '@/api/operation/audit';
  import { get, pick } from 'lodash-es';
  import { computed } from 'vue';
  import { basicBusinessDesc } from './infoData';
  import { formatFields } from '@/utils/business';
  import { useGo } from '@/hooks/web/usePage';
  import ButtonAction from '@/components/Business/ButtonAction.vue';
  import { SModalForm, useSModalForm } from '@/components/SModal';
  import { basiceSchema } from './data';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { showToBadge, showToTag } from '@/components/RenderVnode';
  import { AuditTypeArray, AuditApplyTypeArray, AuditStatusArray } from '@/maps/audit';

  const go = useGo();
  const route = useRoute();
  // 获取数据

  const {
    apiResult: data,
    loading,
    reload,
  } = useApiLoading({
    api: apiAduitDetail,
    params: route.params,
  });
  const [registerModal, { addModal }] = useSModalForm({
    schemas: basiceSchema,
    addFn: apiAuditHandle,
  });

  const renderName = computed(() => {
    return formatFields(data.value, ['submitBy', 'submitPhone']);
  });

  // 是否需要审核
  const isAudit = computed(() => {
    return get(data.value, 'state') === 'init';
  });

  const method = {
    audit() {
      addModal({
        title: '审核',
        record: pick(route.params, ['id']),
      });
    },
  };
</script>

<template>
  <page-wrapper v-loading="loading" @back="() => go(-1)" title="详情">
    <template #headerContent>
      <BasicUser :username="renderName" :avatar="get(data, 'expand.photo')">
        <component :is="showToTag({ text: get(data, 'type'), arr: AuditTypeArray })" />
        <component
          :is="showToTag({ text: get(data, 'applyType'), arr: AuditApplyTypeArray, isShow: false })"
        />
        <component :is="showToBadge({ text: get(data, 'state'), arr: AuditStatusArray })" />
      </BasicUser>
    </template>

    <div>
      <div class="mb-4">
        <DividerTitle :line="false" title="基础信息" />
        <a-card>
          <Description :column="2" :schema="basicBusinessDesc" :data="data" :bordered="false" />
        </a-card>
      </div>
    </div>
    <template #leftFooter>
      <ButtonAction
        @cancel="() => go(-1)"
        :show-save="isAudit"
        saveText="审核"
        @submit="method.audit"
      />
    </template>
    <SModalForm @register="registerModal" @success="() => reload()" />
  </page-wrapper>
</template>

<style lang="less" scoped></style>
