import { BasicColumn, FormSchema } from '@/components/Table';
import { CouponCodeStateArray } from '@/maps/coupon';
import { showToTag } from '@/components/RenderVnode';

export const listColumns: BasicColumn[] = [
  {
    title: '分类名称',
    dataIndex: 'name',
  },
  {
    title: '消费券类型',
    dataIndex: 'code',
    customRender: ({ text }) => {
      return showToTag({ text, arr: CouponCodeStateArray });
    },
  },
  {
    title: '分类描述',
    dataIndex: 'remark',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const categorySchema: FormSchema[] = [
  {
    field: 'name',
    label: '分类名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'code',
    label: '消费券类型',
    component: 'Select',
    required: true,
    dynamicDisabled: true,
    componentProps: {
      options: CouponCodeStateArray,
    },
  },
  {
    field: 'remark',
    label: '分类描述',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 100,
      showCount: true,
    },
  },
  {
    field: 'type',
    label: '分类类型',
    component: 'Input',
    show: false,
    defaultValue: 'COUPON',
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const searchSchema: FormSchema[] = [];
