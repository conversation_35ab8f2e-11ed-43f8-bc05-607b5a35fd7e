import { BasicColumn, FormSchema } from '@/components/Table';
import { CouponUseStateArray } from '@/maps/coupon';
import { showToTag } from '@/components/RenderVnode';
import { get } from 'lodash-es';
import { EducationArray } from '@/maps/member';

export const listColumns: BasicColumn[] = [
  {
    title: '用户昵称',
    dataIndex: 'nickName',
    customRender: ({ record }) => {
      return get(record, 'operationMember.nickname', '--');
    },
  },
  {
    title: '用户姓名',
    dataIndex: 'name',
    customRender: ({ record }) => {
      return get(record, 'operationMember.name', '--');
    },
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    customRender: ({ record }) => {
      return get(record, 'operationMember.phone', '--');
    },
  },
  {
    title: '学历',
    dataIndex: 'education',
    customRender: ({ record }) => {
      const education = get(record, 'operationMember.education', null);
      return education ? showToTag({ text: education, arr: EducationArray }) : '--';
    },
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    customRender: ({ record }) => {
      return get(record, 'operationMember.idCard', '--');
    },
    width: 220,
  },
  {
    title: '消费券码',
    dataIndex: 'id',
    width: 220,
  },
  // 状态;是否使用,可用值:ALL,AWAIT,INIT,USE,OVERDUE
  {
    title: '消费券状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      console.log(text);
      return showToTag({ text, arr: CouponUseStateArray });
    },
  },
  {
    title: '领券时间',
    dataIndex: 'createTime',
    width: 180,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'nickName',
    label: '用户昵称',
    component: 'Input',
  },
  {
    field: 'name',
    label: '用户姓名',
    component: 'Input',
  },
  {
    field: 'phone',
    label: '联系电话',
    component: 'Input',
  },
  {
    field: 'idCard',
    label: '身份证号',
    component: 'Input',
  },
  {
    field: 'couponId',
    label: '消费券码',
    component: 'Input',
  },
  {
    field: 'state',
    label: '消费券状态',
    component: 'Select',
    componentProps: {
      options: CouponUseStateArray,
    },
  },
];
