<template>
  <page-wrapper title="券领取记录">
    <template #extra>
      <a-button v-if="false" preIcon="ant-design:plus-outlined" type="primary"> 新增分类 </a-button>
    </template>

    <BasicTable @register="registerTable" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable } from '@/components/Table';
  import { listColumns, searchSchema } from './data';
  import { apiCouponCollectPage } from '@/api/operation/coupon';

  const [registerTable] = useTable({
    columns: listColumns,
    api: apiCouponCollectPage,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
  });
</script>
