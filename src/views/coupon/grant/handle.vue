<template>
  <div>
    <SModal
      isControl
      :okText="showOkButtonText"
      width="1800px"
      :title="title"
      @register="registerModal"
      @on-ok="methods.okOk"
      @on-cancel="methods.close"
      :loading="loading"
      destroyOnClose
    >
      <div v-show="currStep === 0" class="min-h-300px">
        <BasicTable @register="registerTable" />
      </div>
      <div v-show="currStep === 1" class="min-h-300px">
        <BasicForm
          :show-action-button-group="false"
          :schemas="selectSchemas"
          :baseColProps="{ span: 24 }"
          layout="vertical"
          ref="formEl"
          @submit="methods.submit"
        />
      </div>
      <div v-show="currStep === 2" class="min-h-300px">
        <Result status="success" title="操作成功" sub-title="优惠券已发放" />
      </div>
    </SModal>
  </div>
</template>

<script lang="ts" setup>
  import { SModal, useSModalInner } from '@/components/SModal';
  import { userColumns, searchUserSchema, selectSchemas } from './datas';
  import { BasicTable, useTable } from '@/components/Table';
  import { apiMemberPage } from '@/api/operation/member';
  import { StepActionArray } from '@/maps/stepMap';
  import { computed, ref, unref, useTemplateRef } from 'vue';
  import { matchMap } from '@/utils/tool';
  import { message, Result } from 'ant-design-vue';
  import { cloneDeep, size, get } from 'lodash-es';
  import { BasicForm } from '@/components/Form';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { apiCouponBatchRelease } from '@/api/operation/coupon';

  const formRef = useTemplateRef('formEl');
  const emit = defineEmits(['success', 'register']);

  const { loading, reload } = useApiLoading({
    api: apiCouponBatchRelease,
    immediate: false,
    watchParams: false,
  });
  const currStep = ref(0);
  const innerData = ref({});
  const title = computed(() => {
    // 满减
    const isFullReduce = `[${get(unref(innerData), 'title')}/满${get(unref(innerData), 'threshold')}减${get(unref(innerData), 'price')}]`;
    return `发放消费券-${isFullReduce}`;
  });
  const searchInfo = computed(() => {
    return {
      // categoryTypeId: get(unref(innerData), 'categoryId'),
      couponId: get(unref(innerData), 'id'),
    };
  });
  const [registerModal, { closeModal }] = useSModalInner({
    open: (res) => {
      currStep.value = 0;
      innerData.value = cloneDeep(res);
      console.log(res);
    },
  });
  const [registerTable, { getSelectRowKeys, clearSelectedRowKeys }] = useTable({
    columns: userColumns,
    api: apiMemberPage,
    rowKey: 'id',
    tableId: 'coupon_grant_handle',
    useSearchForm: true,

    showSelectionBar: true,
    size: 'small',
    rowSelection: {
      type: 'checkbox',
    },
    formConfig: {
      schemas: searchUserSchema,
    },
    searchInfo,
  });

  const currStepAction = computed(() => {
    return matchMap(currStep.value, StepActionArray);
  });

  const showOkButtonText = computed(() => {
    return currStepAction.value.label;
  });

  const methods = {
    okOk() {
      if (currStep.value === 0) {
        const keys = getSelectRowKeys();
        if (!size(keys)) {
          return message.error('请选择用户');
        }
        currStep.value++;
        console.log(keys);
      } else if (currStep.value === 1) {
        // 提交
        formRef.value?.submit();
      } else if (currStep.value === 2) {
        closeModal();
      }
    },
    async submit(v: any) {
      console.log(v);
      reload({
        ...v,
        couponId: get(unref(innerData), 'id'),
        memberIds: getSelectRowKeys(),
      }).then(() => {
        currStep.value++;
        emit('success');
      });
    },
    close() {
      clearSelectedRowKeys();
      closeModal();
    },
  };
</script>

<style lang="less" scoped></style>
