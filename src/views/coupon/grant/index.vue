<template>
  <page-wrapper title="消费券发放">
    <template #extra>
      <a-button preIcon="ant-design:plus-outlined" type="primary" @click="method.add()">
        新增平台消费券
      </a-button>
    </template>
    <BasicTable size="small" @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawer" @success="reload()" />
    <handle @register="registerModal" @success="reload()" />
    <infoDrawerCom @register="registerSDrawer" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { useSDrawerForm, SDrawerForm, useSDrawer } from '@/components/SDrawer';
  import { basicColumns, basicSchema, searchSchema } from './datas';
  import {
    apiCouponPage,
    apiCouponAdd,
    apiCouponOperate,
    apiCouponEdit,
  } from '@/api/operation/coupon';
  import { useSModal } from '@/components/SModal';
  // import { useGo } from '@/hooks/web/usePage';
  // import { PageEnum } from '@/enums/pageEnum';
  import { get, map } from 'lodash-es';
  import handle from './handle.vue';
  import infoDrawerCom from './info-drawer-com.vue';

  // const go = useGo();

  const [registerTable, { reload }] = useTable({
    columns: basicColumns,
    api: apiCouponPage,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerModal, { openModal }] = useSModal();

  const [registerDrawer, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiCouponAdd,
    updateFn: apiCouponEdit,
  });

  const [registerSDrawer, { openDrawer }] = useSDrawer();

  const method = {
    add() {
      addDrawer({
        title: '新增消费券',
      });
    },
    info(record: Recordable) {
      // go({
      //   path: PageEnum.COUPON_INFO,
      //   query: {
      //     id: record.id,
      //   },
      // });
      openDrawer({
        record,
      });
    },
    garnt(record: Recordable) {
      openModal(record);
    },
    // 下架
    async off(_record: Recordable) {
      await apiCouponOperate({
        id: get(_record, 'id'),
        state: true,
      });
      reload();
    },
    edit(record: Recordable) {
      updateDrawer({
        record: {
          ...record,
          storeCategoryIdList: map(get(record, 'relevancyCouponStores', []) || [], (item) => {
            return item.storeCategoryId;
          }),
        },
      });
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      // {
      //   label: '详情',
      //   onClick: method.info.bind(null, record),
      // },
      // {
      //   label: '下架',
      //   ifShow: () => get(record, 'state') === 'NORMAL',
      //   popConfirm: {
      //     title: '确定下架吗？',
      //     confirm: method.off.bind(null, record),
      //   },
      // },

      {
        label: '编辑',
        onClick: method.edit.bind(null, record),
      },
      {
        label: '发放',
        onClick: method.garnt.bind(null, record),
        ifShow: () => {
          return record.state === 'NORMAL';
        },
      },
      {
        label: '发放',
        disabled: true,
        ifShow: () => {
          return record.state !== 'NORMAL';
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
