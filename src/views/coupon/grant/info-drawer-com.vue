<template>
  <div>
    <SDrawer
      @register="register"
      width="800px"
      :is-ok="false"
      title="消费券详情"
      cancel-text="关闭"
      closable
    >
      <div v-loading="loading">
        <DividerTitle title="基础信息" />
        <a-card>
          <Description :column="2" :schema="basicDesc" :data="resultData" :bordered="false" />
        </a-card>
        <DividerTitle title="发放用户" />
        <BasicTable @register="registerTable" />
      </div>
    </SDrawer>
  </div>
</template>

<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { useSDrawerInner, SDrawer } from '@/components/SDrawer';
  import { apiCouponInfo, apiCouponGrantmemberListByCouponIdPage } from '@/api/operation/coupon';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { Description } from '@/components/Description';
  import DividerTitle from '@/components/Form/src/extend/DividerTitle.vue';
  import { basicDesc, userColumns } from '@/views/coupon/platform/data';
  import { BasicTable, useTable } from '@/components/Table';

  defineEmits(['register']);
  const searchForm = reactive({
    id: '',
  });

  const {
    apiResult: resultData,
    loading,
    reload,
  } = useApiLoading({
    api: apiCouponInfo,
    params: searchForm,
    immediate: false,
  });
  const [registerTable, { reload: reloadTable }] = useTable({
    columns: userColumns,
    api: apiCouponGrantmemberListByCouponIdPage,
    searchInfo: searchForm,
    useSearchForm: false,
    immediate: false,
  });

  const cacheRecord = ref<any>({});

  const [register] = useSDrawerInner(async (data) => {
    const { record } = data;
    cacheRecord.value = record;
    searchForm.id = record.id;
    reload && reload();
    reloadTable && reloadTable();
  });
</script>

<style lang="less" scoped></style>
