import { BasicColumn, FormSchema } from '@/components/Table';
import { get } from 'lodash-es';
import { CouponStateArray } from '@/maps/coupon';
import { showToBadge } from '@/components/RenderVnode';
import { DescItem } from '@/components/Description';
import { apiCategoryList } from '@/api/operation/category';

export const listColumns: BasicColumn[] = [
  {
    title: '消费券名称',
    dataIndex: 'title',
  },
  {
    title: '支持的商户类别',
    dataIndex: 'storeType',
    customRender: ({ record }) => {
      return get(record, 'relevancyCouponStores', [])
        ?.map((item) => item.storeCategoryName)
        .join(',');
    },
  },
  {
    title: '消费券码',
    dataIndex: 'id',
  },

  {
    title: '所属分类',
    dataIndex: 'category',
    customRender: ({ record }) => {
      return get(record, 'operationCategory.name', '--');
    },
  },
  {
    title: '消费券面额',
    dataIndex: 'price',
    customRender: ({ record }) => {
      return `满${get(record, 'threshold')}减${get(record, 'price')}`;
    },
  },
  {
    title: '有效期',
    dataIndex: 'beginDate',
    customRender: ({ record }) => {
      return `${get(record, 'beginDate')}至${get(record, 'endDate')}`;
    },
  },
  {
    title: '状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      return showToBadge({
        text,
        arr: CouponStateArray,
      });
    },
  },
  {
    title: '发布时间',
    dataIndex: 'createTime',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'storeCategoryIdList',
    label: '支持商户类别',
    component: 'ApiSelect',
    required: true,
    defaultValue: [],
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'STORE' });
      },
      mode: 'multiple',
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'title',
    label: '优惠券名称',
    component: 'Input',
    required: true,
  },

  {
    field: 'categoryId',
    label: '所属分类',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'COUPON' });
      },
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'threshold',
    label: '满',
    component: 'InputNumber',
    componentProps: ({ formActionType, formModel }) => {
      return {
        min: 1,
        max: 99999999,
        // 不支持小数点
        precision: 0,
        style: {
          width: '100%',
        },
        onChange: () => {
          if (get(formModel, 'price')) {
            setTimeout(() => {
              formActionType.validateFields(['price']);
            }, 1);
          }
        },
      };
    },
    dynamicRules: ({ values }) => {
      return [
        {
          required: true,
          message: '请输入满减金额',
        },
        {
          validator: (_, value) => {
            if (value && values.price && value <= values.price) {
              return Promise.reject('满减金额不能大于或等于优惠券金额');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },
  {
    field: 'price',
    label: '减',
    component: 'InputNumber',
    componentProps: ({ formActionType, formModel }) => {
      return {
        min: 1,
        max: 99999999,
        style: {
          width: '100%',
        },
        onChange: () => {
          if (get(formModel, 'threshold')) {
            setTimeout(() => {
              formActionType.validateFields(['threshold']);
            }, 1);
          }
        },
        // 不支持小数点
        precision: 0,
      };
    },
    dynamicRules: ({ values }) => {
      return [
        {
          required: true,
          message: '请输入满减金额',
        },
        {
          validator: (_, value) => {
            if (value && values.threshold && value >= values.threshold) {
              return Promise.reject('满减金额不能大于或等于优惠券金额');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },
  {
    field: '[beginDate,endDate]',
    label: '有效期',
    component: 'RangePicker',
    required: true,
    componentProps: {
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      style: {
        width: '100%',
      },
    },
  },
  {
    field: 'count',
    label: '最大发放数量',
    required: true,
    component: 'InputNumber',
    componentProps: {
      min: 1,
      max: 99999999,
      style: {
        width: '100%',
      },
      // 不支持小数点
      precision: 0,
    },
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'categoryId',
    label: '消费券分类',
    component: 'ApiSelect',
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'COUPON' });
      },
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'title',
    label: '消费券名称',
    component: 'Input',
  },
  {
    field: 'code',
    label: '消费券码',
    component: 'Input',
    componentProps: {
      maxlength: 40,
    },
  },
  {
    field: 'state',
    label: '状态',
    component: 'Select',
    componentProps: {
      options: CouponStateArray,
    },
  },
];

export const basicDesc: DescItem[] = [
  {
    label: '消费券名称',
    field: 'title',
  },
  {
    label: '消费券码',
    field: 'id',
  },
  {
    label: '消费券面额',
    field: 'price',
    render: (_, record) => {
      return `满${get(record, 'threshold')}减${get(record, 'price')}`;
    },
  },
  {
    label: '分类',
    field: 'a',
    render(_, record) {
      return get(record, 'operationCategory.name', '--');
    },
  },
  {
    label: '有效期',
    field: 'b',
    render: (_, record) => {
      return `${get(record, 'beginDate')}至${get(record, 'endDate')}`;
    },
  },
  {
    label: '状态',
    field: 'state',
    render: (text) =>
      showToBadge({
        text,
        arr: CouponStateArray,
      }),
  },
  {
    label: '发布时间',
    field: 'createTime',
  },
];

export const userColumns: BasicColumn[] = [
  {
    title: '用户昵称',
    dataIndex: 'nickname',
    customRender: ({ record }) => {
      return get(record, 'operationMember.nickname', '--');
    },
  },
  {
    title: '用户姓名',
    dataIndex: 'name',
    customRender: ({ record }) => {
      return get(record, 'operationMember.name', '--');
    },
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    customRender: ({ record }) => {
      return get(record, 'operationMember.phone', '--');
    },
  },
];
