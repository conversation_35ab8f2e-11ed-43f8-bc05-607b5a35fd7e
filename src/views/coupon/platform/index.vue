<template>
  <page-wrapper title="平台消费券管理">
    <template #extra>
      <a-button preIcon="ant-design:plus-outlined" type="primary" @click="method.add()">
        新增平台消费券
      </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawer" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';
  import { listColumns, basicSchema, searchSchema } from './data';
  import { apiCouponPage, apiCouponOperate, apiCouponAdd } from '@/api/operation/coupon';
  import { get } from 'lodash-es';
  import { useGo } from '@/hooks/web/usePage';
  import { PageEnum } from '@/enums/pageEnum';

  const go = useGo();

  const [registerTable, { reload }] = useTable({
    columns: listColumns,
    api: apiCouponPage,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerDrawer, { addDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiCouponAdd,
  });

  const method = {
    add() {
      addDrawer({
        title: '新增消费券',
      });
    },
    info(record: Recordable) {
      go({
        path: PageEnum.COUPON_INFO,
        query: {
          id: record.id,
        },
      });
    },
    // 下架
    async off(_record: Recordable) {
      await apiCouponOperate({
        id: get(_record, 'id'),
        state: true,
      });
      reload();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: method.info.bind(null, record),
      },
      {
        label: '下架',
        ifShow: () => get(record, 'state') === 'NORMAL',
        popConfirm: {
          title: '确定下架吗？',
          confirm: method.off.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
