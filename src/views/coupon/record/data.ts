import { BasicColumn, FormSchema } from '@/components/Table';
import { get } from 'lodash-es';
import { DescItem } from '@/components/Description';

export const listColumns: BasicColumn[] = [
  {
    title: '消费券名称',
    dataIndex: 'title',
    customRender: ({ record }) => {
      return get(record, 'operationCoupon.title', '--');
    },
  },
  {
    title: '发放券额',
    dataIndex: 'price',
    customRender: ({ record }) => {
      return `满${get(record, 'operationCoupon.threshold')}减${get(record, 'operationCoupon.price')}`;
    },
  },
  {
    title: '券码',
    dataIndex: 'couponId',
  },
  {
    title: '每人发放数量',
    dataIndex: 'count',
  },
  {
    title: '发放人数',
    dataIndex: 'peopleCount',
  },
  {
    title: '发放人',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const categorySchema: FormSchema[] = [
  {
    field: 'name',
    label: '分类名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'remark',
    label: '分类描述',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 100,
      showCount: true,
    },
  },
  {
    field: 'type',
    label: '分类类型',
    component: 'Input',
    show: false,
    defaultValue: 'COUPON',
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'couponId',
    label: '发放券码',
    component: 'Input',
    componentProps: {
      maxlength: 40,
    },
  },
  {
    field: '[beginDate,endDate]',
    label: '发放时间',
    component: 'RangePicker',
    componentProps: {
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['发放开始时间', '发放截止时间'],
      style: {
        width: '100%',
      },
    },
  },
];

export const basicDesc: DescItem[] = [
  {
    field: 'price',
    label: '发放券额',
    render: (_, record) => {
      return get(record, 'operationCoupon.price', '0');
    },
  },
  {
    field: 'batchNo',
    label: '券码',
  },
  {
    label: '每人发放数量',
    field: 'count',
  },
  {
    label: '发放人数',
    field: 'peopleCount',
  },
  {
    label: '发放人',
    field: 'createBy',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
];

export const userColumns: BasicColumn[] = [
  {
    title: '用户昵称',
    dataIndex: 'nickname',
    customRender: ({ record }) => {
      return get(record, 'operationMember.nickname', '--');
    },
  },
  {
    title: '用户姓名',
    dataIndex: 'name',
    customRender: ({ record }) => {
      return get(record, 'operationMember.name', '--');
    },
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    customRender: ({ record }) => {
      return get(record, 'operationMember.phone', '--');
    },
  },
];
