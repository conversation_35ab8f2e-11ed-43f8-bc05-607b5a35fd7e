<template>
  <page-wrapper title="券发放记录">
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <infoDrawerCom @register="registerSDrawer" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { listColumns, searchSchema } from './data';
  import { apiCouponGrantPage } from '@/api/operation/coupon';
  // import { useGo } from '@/hooks/web/usePage';
  // import { PageEnum } from '@/enums/pageEnum';
  import infoDrawerCom from './info-drawer-com.vue';
  import { useSDrawer } from '@/components/SDrawer';

  // const go = useGo();

  const [registerTable] = useTable({
    columns: listColumns,
    api: apiCouponGrantPage,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });
  const [registerSDrawer, { openDrawer }] = useSDrawer();
  const method = {
    info: (record: Recordable) => {
      // go({
      //   path: PageEnum.RECORD_COUPON_INFO,
      //   query: {
      //     id: record.id,
      //   },
      // });
      openDrawer({
        record,
      });
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: method.info.bind(null, record),
      },
    ];
  }
</script>

<style lang="less" scoped></style>
