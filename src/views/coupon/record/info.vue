<template>
  <page-wrapper @back="() => $router.go(-1)" title="详情">
    <div>
      <DividerTitle title="基本信息" />
      <a-card>
        <Description :column="2" :schema="basicDesc" :data="data" :bordered="false" />
      </a-card>
      <DividerTitle title="发放用户" />
      <BasicTable @register="registerTable" />
    </div>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import DividerTitle from '@/components/Form/src/extend/DividerTitle.vue';
  import { apiCouponGrantId, apiCouponGrantMemberListIdPage } from '@/api/operation/coupon';
  import { message } from 'ant-design-vue';
  import { Description } from '@/components/Description';
  import { useRoute } from 'vue-router';
  import { get } from 'lodash-es';
  import { ref } from 'vue';
  import { basicDesc, userColumns } from './data';
  import { BasicTable, useTable } from '@/components/Table';

  const route = useRoute();
  const data = ref({});

  const [registerTable] = useTable({
    columns: userColumns,
    api: apiCouponGrantMemberListIdPage,
    searchInfo: {
      id: get(route, 'query.id'),
    },
    useSearchForm: false,
  });
  const methods = {
    async fetchData() {
      const msg = message.loading('加载中...', 0);
      try {
        data.value = await apiCouponGrantId(get(route, 'query'));
        console.log(data.value);
      } finally {
        msg();
      }
    },
  };

  methods.fetchData();
</script>

<style lang="less" scoped></style>
