import { BasicColumn, FormSchema } from '@/components/Table';
import { get } from 'lodash-es';
import { apiStoreList } from '@/api/operation/store';
import { EducationArray } from '@/maps/member';
import { showToTag } from '@/components/RenderVnode';

export const listColumns: BasicColumn[] = [
  {
    title: '消费券名称',
    dataIndex: 'title',
    customRender: ({ record }) => {
      return get(record, 'operationCoupon.title', '--');
    },
  },
  {
    title: '核销券额',
    dataIndex: 'title',
    customRender: ({ record }) => {
      return `满${get(record, 'operationCoupon.threshold')}减${get(record, 'operationCoupon.price')}`;
    },
  },
  {
    title: '核销券码',
    dataIndex: 'id',
  },
  {
    title: '核销商家',
    dataIndex: 'createBy',
    customRender: ({ record }) => {
      return get(record, 'operationStore.title', '--');
    },
  },
  {
    title: '核销时间',
    dataIndex: 'useTime',
  },
  // {
  //   title: '用户昵称',
  //   dataIndex: 'name',
  //   customRender: ({ record }) => {
  //     return get(record, 'operationMember.name', '--');
  //   },
  // },
  // {
  //   title: '用户手机号',
  //   dataIndex: 'phone',
  //   customRender: ({ record }) => {
  //     return get(record, 'operationMember.phone', '--');
  //   },
  // },
  // 【消费券码】、【姓名】、【身份证号】、【联系电话】、【学历】、【公司名称】
  {
    title: '消费券码',
    dataIndex: 'couponId',
  },
  {
    title: '用户昵称',
    dataIndex: 'memberName',
    customRender: ({ record }) => {
      return get(record, 'operationMember.name', '--');
    },
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: 220,
    customRender: ({ record }) => {
      return get(record, 'operationMember.idCard', '--');
    },
  },
  {
    title: '用户手机号',
    dataIndex: 'phone',
    customRender: ({ record }) => {
      return get(record, 'operationMember.phone', '--');
    },
  },
  {
    title: '学历',
    dataIndex: 'education',
    customRender: ({ record }) => {
      const education = get(record, 'operationMember.education', null);
      return education ? showToTag({ text: education, arr: EducationArray }) : '--';
    },
  },
  {
    title: '公司名称',
    dataIndex: 'enterpriseName',
    customRender: ({ record }) => {
      return get(record, 'operationMember.enterpriseName', '--');
    },
  },
];

export const categorySchema: FormSchema[] = [
  {
    field: 'name',
    label: '分类名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'remark',
    label: '分类描述',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 100,
      showCount: true,
    },
  },
  {
    field: 'type',
    label: '分类类型',
    component: 'Input',
    show: false,
    defaultValue: 'COUPON',
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const searchSchema: FormSchema[] = [
  {
    label: '核销券码',
    field: 'code',
    component: 'Input',
  },
  {
    label: '核销商家',
    field: 'storeId',
    component: 'ApiSelect',
    componentProps: {
      api: apiStoreList,
      labelField: 'title',
      valueField: 'id',
    },
  },
  {
    field: '[beginTime,endTime]',
    label: '核销时间',
    component: 'RangePicker',
    componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['核销时间开始', '核销时间结束'],
      style: {
        width: '100%',
      },
    },
  },
  // 【消费券码】、【姓名】、【身份证号】、【联系电话】、【学历】、【公司名称】
  {
    field: 'couponId',
    label: '消费券码',
    component: 'Input',
  },
  {
    field: 'memberName',
    label: '姓名',
    component: 'Input',
  },
  {
    field: 'idCard',
    label: '身份证号',
    component: 'Input',
  },
  {
    field: 'phone',
    label: '联系电话',
    component: 'Input',
  },
  {
    field: 'education',
    label: '学历',
    component: 'Select',
    componentProps: {
      options: EducationArray,
    },
  },
  {
    field: 'enterpriseName',
    label: '公司名称',
    component: 'Input',
  },
];
