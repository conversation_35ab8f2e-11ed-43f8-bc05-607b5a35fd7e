<template>
  <page-wrapper title="消费券管理">
    <template #extra>
      <a-button
        preIcon="material-symbols:account-balance-wallet-outline"
        type="primary"
        @click="method.batchSettle()"
      >
        批量结算
      </a-button>
      <a-button
        preIcon="material-symbols:download"
        type="primary"
        :loading="isExporting"
        @click="handleExport"
      >
        导出
      </a-button>
    </template>
    <BasicTable :search-info="searchInfo" @register="registerTable">
      <template #tableTitle>
        <div>
          <RadioGroup v-model:value="searchInfo.closing" button-style="solid">
            <RadioButton value="true">已结算</RadioButton>
            <RadioButton value="false">未结算</RadioButton>
          </RadioGroup>
        </div>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawer" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable } from '@/components/Table';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';
  import { listColumns, categorySchema, searchSchema } from './data';
  import { apiCategoryEdit, apiCategoryAdd, apiCategoryDelete } from '@/api/operation/category';
  import { apiMemberCouponWriteOffPage, apiMemberCouponBatchSettle } from '@/api/operation/member';
  import { RadioGroup, RadioButton, Modal, message } from 'ant-design-vue';
  import { reactive, watch, computed } from 'vue';
  import { get, size } from 'lodash-es';
  import { TableRowSelection } from '@/components/Table/src/types/table';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { apiCouponExport } from '@/api/operation/coupon';
  import { to } from '@bryce-loskie/utils';
  import { downloadByData } from '@/utils/file/download';

  const searchInfo = reactive({
    closing: 'true',
  });

  watch(
    () => searchInfo.closing,
    () => {
      clearSelectedRowKeys && clearSelectedRowKeys();
    },
  );

  const rowSelection = computed<TableRowSelection | undefined>(() => {
    if (searchInfo.closing === 'true') {
      return undefined;
    }
    return {
      type: 'checkbox',
    };
  });

  const [registerTable, { reload, getRowSelection, clearSelectedRowKeys, getForm }] = useTable({
    formConfig: {
      schemas: searchSchema,
    },
    columns: listColumns,
    api: apiMemberCouponWriteOffPage,
    useSearchForm: true,
    showSelectionBar: true,
    rowSelection,
    tableSetting: {
      settingCache: true,
      selectCache: false,
    },
  });

  const { reload: rawHandleExport, loading: isExporting } = useApiLoading({
    api: apiCouponExport,
    immediate: false,
  });

  const handleExport = async () => {
    const payload = getForm().getFieldsValue();
    const [err, res] = await to(rawHandleExport({ ...payload, ...searchInfo }));
    if (err) {
      message.error('导出失败, 请稍后重试');
      return;
    }

    const { headers, data } = res;
    const filename =
      headers['content-disposition']?.split("filename*=utf-8''")[1]?.split(';')?.[0] ||
      '消费券结算.xlsx';
    const contentType =
      headers['content-type']?.split(';')?.[0] ||
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    downloadByData(data, filename, contentType);
  };

  const [registerDrawer, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: categorySchema,
    addFn: apiCategoryAdd,
    updateFn: apiCategoryEdit,
  });

  const method = {
    add() {
      addDrawer({
        title: '新增消费券分类',
      });
    },
    edit(record: Recordable) {
      updateDrawer({
        record,
      });
    },
    async del(record: Recordable) {
      await apiCategoryDelete(record.id);
      reload();
    },
    // 批量结算
    async batchSettle() {
      const keys = get(getRowSelection(), 'selectedRowKeys');

      if (size(keys) === 0) {
        return message.error('请选择要结算的消费券');
      }
      Modal.confirm({
        title: '批量结算',
        content: '您已选择结算以下记录，请核对信息无误后，点击“确认结算”以继续操作',
        okText: '确认结算',
        cancelText: '取消',
        onOk: async () => {
          await apiMemberCouponBatchSettle(keys);
          clearSelectedRowKeys();
          // 批量结算
          reload();
        },
      });
    },
  };
</script>
