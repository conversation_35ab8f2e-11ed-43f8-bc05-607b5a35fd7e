<script lang="ts" setup>
  import { BasicForm, useForm } from '@/components/Form';
  import { Tinymce } from '@/components/Tinymce';
  import { h } from 'vue';

  const [register] = useForm({
    labelWidth: 120,
    baseColProps: { span: 24 },
    schemas: [
      {
        field: 'field9',
        label: '字段10',
        component: 'InputList',
      },
      {
        field: 'introduction',
        component: 'Input',
        label: '房源介绍',
        defaultValue: '',
        rules: [{ required: true }],
        render: ({ model, field }) => {
          return h(Tinymce, {
            plugins: ['wordcount image link'],
            toolbar: [
              'fontsizeselect bold italic underline forecolor image link alignleft aligncenter alignright',
            ],
            value: model[field],
            onChange: (value: string) => {
              model[field] = value;
            },
          });
        },
      },
    ],
  });

  function submit(v) {
    console.log(v);
  }
</script>

<template>
  <div>
    <a-card>
      <BasicForm @register="register" @submit="submit" />
    </a-card>
  </div>
</template>

<style lang="less" scoped></style>
