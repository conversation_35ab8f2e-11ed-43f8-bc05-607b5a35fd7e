<template>
  <page-wrapper v-loading="loading" title="人才学历补贴申请指南">
    <div>
      <BasicForm
        :show-action-button-group="false"
        :schemas="applyGuideSchema"
        :baseColProps="{ span: 24 }"
        layout="vertical"
        ref="formEl"
        @submit="methods.submit"
        :model="apiResult"
      />
    </div>
    <template #leftFooter>
      <ButtonAction :loading="saveLoading" @submit="methods.save" />
    </template>
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { ref, unref, useTemplateRef } from 'vue';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { applyGuideSchema } from './data';
  import { apiPolicyInfo, apiPolicyAdd } from '@/api/operation/policy';
  import { BasicForm } from '@/components/Form';
  import ButtonAction from '@/components/Business/ButtonAction.vue';

  const formRef = useTemplateRef('formEl');
  const params = ref({
    type: 'education',
  });
  const {
    loading,
    reload: reloadInfo,
    apiResult,
  } = useApiLoading({
    api: apiPolicyInfo,
    params,
    immediate: true,
    watchParams: false,
  });
  const { loading: saveLoading, reload: apiSave } = useApiLoading({
    api: apiPolicyAdd,
    immediate: false,
    watchParams: false,
  });
  const methods = {
    submit(v) {
      console.log('submit');
      apiSave({
        ...v,
        ...unref(params),
      }).then(() => {
        reloadInfo();
      });
    },

    save() {
      unref(formRef)?.submit();
    },
  };
</script>

<style lang="less" scoped></style>
