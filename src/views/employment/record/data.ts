import { showToBadge, showToTag } from '@/components/RenderVnode';
import { BasicColumn, FormSchema } from '@/components/Table';
import { AuditTypeArray, AuditStatusArray } from '@/maps/audit';

export const basicColumns: BasicColumn[] = [
  {
    title: '申请人',
    dataIndex: 'submitBy',
  },
  {
    title: '联系电话',
    dataIndex: 'submitPhone',
  },
  {
    title: '申请类型',
    dataIndex: 'type',
    customRender: ({ text }) => {
      return showToTag({ text, arr: AuditTypeArray });
    },
  },
  {
    title: '状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      return showToBadge({ text, arr: AuditStatusArray });
    },
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '申请时间',
    dataIndex: 'submitTime',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'searchWord',
    label: '申请人',
    component: 'Input',
  },
  {
    field: '[startDate,endDate]',
    label: '申请时间',
    component: 'RangePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
      style: {
        width: '100%',
      },
    },
  },
];
