import { BasicColumn, FormSchema } from '@/components/Table';
import { phoneReg } from '@/utils/validate';

export const columns: BasicColumn[] = [
  {
    title: '企业名称',
    dataIndex: 'name',
  },
  {
    title: '联系人',
    dataIndex: 'linkman',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
  },
  {
    title: '企业地址',
    dataIndex: 'address',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'keyword',
    label: '企业名称',
    component: 'Input',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '企业名称',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入企业名称',
      maxlength: 50,
      showCount: true,
    },
  },
  {
    field: 'linkman',
    label: '联系人',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入联系人',
    },
  },
  {
    field: 'phone',
    label: '联系电话',
    component: 'Input',
    required: true,
    rules: [phoneReg],
  },
  {
    field: 'address',
    label: '企业地址',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      placeholder: '请输入企业地址',
      maxlength: 50,
      rows: 4,
      showCount: true,
    },
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];
