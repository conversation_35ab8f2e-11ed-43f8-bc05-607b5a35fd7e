<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { columns, formSchema, searchSchema } from './data';
  import {
    apiEnterprisePage,
    apiEnterpriseAdd,
    apiEnterpriseEdit,
    apiEnterpriseDelete,
  } from '@/api/operation/enterprise';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';
  import { get } from 'lodash-es';

  const [registerTable, { reload }] = useTable({
    columns,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
    api: apiEnterprisePage,
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerDrawer, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: formSchema,
    addFn: apiEnterpriseAdd,
    updateFn: apiEnterpriseEdit,
  });

  const methods = {
    add() {
      addDrawer({
        title: '新增企业',
      });
    },
    edit(record) {
      updateDrawer({
        title: '编辑企业',
        record,
      });
    },
    async del(record) {
      const id = get(record, 'id');
      if (!id) {
        return;
      }
      await apiEnterpriseDelete(id);
      reload();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        icon: 'ant-design:edit-outlined',
        tooltip: '编辑',
        onClick: methods.edit.bind(null, record),
      },
      {
        icon: 'ant-design:delete-outlined',
        tooltip: '删除',
        color: 'error',
        popConfirm: {
          title: '确定删除吗？',
          confirm: methods.del.bind(null, record),
        },
      },
    ];
  }
</script>

<template>
  <page-wrapper title="企业管理">
    <template #extra>
      <a-button type="primary" @click="methods.add" preIcon="ant-design:plus-outlined">
        新增企业
      </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawer" @success="reload()" />
  </page-wrapper>
</template>

<style lang="less" scoped></style>
