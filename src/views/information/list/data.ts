import { BasicColumn, FormSchema } from '@/components/Table';
import { Tinymce } from '@/components/Tinymce';
import { h } from 'vue';

export const basicColumns: BasicColumn[] = [
  {
    title: '标题',
    dataIndex: 'title',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'thumbnail',
    label: '缩略图',
    component: 'UploadImage',
    required: true,
  },
  // {
  //   field: 'photo',
  //   label: '图片',
  //   component: 'UploadImage',
  //   required: true,
  // },
  {
    field: 'title',
    label: '标题',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 50,
    },
  },
  // {
  //   field: 'detail',
  //   label: '内容',
  //   component: 'InputTextArea',
  //   required: true,
  //   componentProps: {
  //     rows: 8,
  //     maxlength: 1000,
  //   },
  // },

  {
    field: 'detail',
    label: '内容',
    component: 'InputTextArea',
    required: true,
    render: ({ model, field }) => {
      return h(Tinymce, {
        plugins: ['wordcount image link fullscreen'],
        toolbar: [
          'fontsizeselect bold italic underline forecolor image link alignleft aligncenter alignright fullscreen',
        ],
        value: model[field],
        onChange: (value: string) => {
          model[field] = value;
        },
      });
    },
  },
  {
    field: 'publish',
    label: '是否发布',
    component: 'RadioButtonGroup',
    defaultValue: true,
    componentProps: {
      options: [
        {
          label: '立即发布',
          value: true,
        },
        {
          label: '稍后发布',
          value: false,
        },
      ],
    },
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'title',
    label: '标题',
    component: 'Input',
  },
];
