<template>
  <page-wrapper title="资讯管理">
    <template #extra>
      <a-button preIcon="ant-design:plus-outlined" type="primary" @click="method.add()">
        新增资讯
      </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawer" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';
  import { basicColumns, basicSchema, searchSchema } from './data';

  import {
    apiConsultPage,
    apiConsultAdd,
    apiConsultEdit,
    apiConsultDel,
  } from '@/api/operation/consult';

  const [registerTable, { reload }] = useTable({
    columns: basicColumns,
    api: apiConsultPage,
    useSearchForm: true,
    formConfig: {
      schemas: searchSchema,
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerDrawer, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiConsultAdd,
    updateFn: apiConsultEdit,
  });

  const method = {
    add() {
      addDrawer({
        title: '新增资讯',
      });
    },
    edit(record: Recordable) {
      updateDrawer({
        record,
      });
    },
    async del(record: Recordable) {
      await apiConsultDel(record);
      reload();
    },
    async publish(record: Recordable) {
      await apiConsultEdit({
        id: record.id,
        publish: !record.publish,
      });
      reload();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.edit.bind(null, record),
        ifShow: !record.publish,
      },
      {
        label: '发布',
        ifShow: !record.publish,
        popConfirm: {
          title: '确定发布吗？',
          confirm: method.publish.bind(null, record),
        },
      },
      {
        label: '下架',
        ifShow: record.publish,
        popConfirm: {
          title: '确定下架吗？',
          confirm: method.publish.bind(null, record),
        },
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
