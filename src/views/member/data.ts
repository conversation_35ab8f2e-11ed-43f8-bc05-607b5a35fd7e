import { BasicColumn, FormSchema } from '@/components/Table';
import { DescItem } from '@/components/Description';
import { get } from 'lodash-es';
import { UserCouponStateArray } from '@/maps/coupon';
import { showToBadge, showToTag } from '@/components/RenderVnode';
import { AuditStatusArray } from '@/maps/audit';
import { EducationArray, MemberSexArray } from '@/maps/member';

export const memberColumns: BasicColumn[] = [
  {
    title: '用户昵称',
    dataIndex: 'nickname',
  },
  {
    title: '用户姓名',
    dataIndex: 'name',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
  },
  {
    title: '身份证号',
    dataIndex: 'idCard',
    width: 220,
  },
  {
    title: '公司名称',
    dataIndex: 'enterpriseName',
  },
  {
    title: '学历',
    dataIndex: 'education',
    customRender({ text }) {
      return showToTag({ text, arr: EducationArray });
    },
  },
  {
    title: '人才类别',
    dataIndex: 'talentCategory',
  },
  {
    title: '申请人才补贴',
    dataIndex: 'educationState',
    customRender({ text }) {
      return showToBadge({ text, arr: AuditStatusArray, defaultText: '未申请' });
    },
  },
  {
    title: '申请人才公寓',
    dataIndex: 'apartmentState',
    customRender({ text }) {
      return showToBadge({ text, arr: AuditStatusArray, defaultText: '未申请' });
    },
  },
  {
    title: '申请就业补贴',
    dataIndex: 'employmentState',
    customRender({ text }) {
      return showToBadge({ text, arr: AuditStatusArray, defaultText: '未申请' });
    },
  },
  {
    title: '注册时间',
    dataIndex: 'registerTime',
  },
];

export const memberSearchSchema: FormSchema[] = [
  {
    field: 'searchWord',
    label: '用户姓名',
    component: 'Input',
  },
  {
    field: 'education',
    label: '学历',
    component: 'Select',
    componentProps: {
      options: EducationArray,
    },
  },
  {
    field: 'educationState',
    label: '申请人才补贴',
    component: 'Select',
    componentProps: {
      options: AuditStatusArray,
    },
  },
  {
    field: 'apartmentState',
    label: '申请人才公寓',
    component: 'Select',
    componentProps: {
      options: AuditStatusArray,
    },
  },
  {
    field: 'employmentState',
    label: '申请就业补贴',
    component: 'Select',
    componentProps: {
      options: AuditStatusArray,
    },
  },
  {
    field: '[registerStart,registerEnd]',
    label: '注册时间',
    component: 'RangePicker',
    componentProps: {
      showTime: false,
      valueFormat: 'YYYY-MM-DD',
      placeholder: ['注册开始', '结束时间'],
      style: {
        width: '100%',
      },
    },
  },
];

export const basicMemberDesc: DescItem[] = [
  {
    label: '姓名',
    field: 'name',
  },
  {
    label: '证件号',
    field: 'idCard',
  },
  {
    label: '性别',
    field: 'sex',
    render: (text) => {
      return showToTag({ text, arr: MemberSexArray });
    },
  },
  {
    label: '出生日期',
    field: 'birthday',
  },
  {
    label: '年龄',
    field: 'age',
  },
  {
    label: '移动电话',
    field: 'phone',
  },
];

export const educationMemberDesc: DescItem[] = [
  {
    label: '毕业院校',
    field: 'school',
  },
  {
    label: '学历',
    field: 'education',
    render(text) {
      return showToTag({ text, arr: EducationArray });
    },
  },
  {
    label: '毕业时间',
    field: 'graduationDate',
  },
  {
    label: '所学专业',
    field: 'major',
  },
  {
    label: '学历证明(缺少字段)',
    field: 'graduateSchool',
  },
];

export const enterpriseMemberDesc: DescItem[] = [
  {
    label: '单位名称',
    field: 'enterpriseName',
  },
  {
    label: '进入现单位时间',
    field: 'entryDate',
  },
  {
    label: '单位地址',
    field: 'enterpriseAddress',
  },
  {
    label: '岗位类型',
    field: 'postType',
  },
  {
    label: '与单位签订的劳动合同',
    field: 'laborContract',
  },
  {
    label: '社区缴纳保险记录',
    field: 'socialSecurityAttach',
  },
];

export const backMemberDesc: DescItem[] = [
  {
    label: '开户行名称',
    field: 'bankName',
  },
  {
    label: '开户支行名称',
    field: 'bankBranchName',
  },
  {
    label: '银行卡号',
    field: 'bankCard',
  },
];

// 注册信息
export const registerMemberDesc: DescItem[] = [
  {
    label: '用户昵称',
    field: 'nickname',
  },
  {
    label: '注册时间',
    field: 'registerTime',
  },
];

export const userCouponColumns: BasicColumn[] = [
  {
    title: '消费券名称',
    dataIndex: 'title',
    customRender: ({ record }) => {
      return get(record, 'operationCoupon.title', '--');
    },
  },
  {
    title: '消费券码',
    dataIndex: 'id',
  },

  {
    title: '所属分类',
    dataIndex: 'category',
    customRender: ({ record }) => {
      return get(record, 'operationCoupon.operationCategory.name', '--');
    },
  },
  {
    title: '支持的商户类别',
    dataIndex: 'storeType',
    customRender: ({ record }) => {
      return get(record, 'operationCoupon.relevancyCouponStores', [])
        ?.map((item) => item.storeCategoryName)
        .join(',');
    },
  },
  {
    title: '消费券面额',
    dataIndex: 'price',
    customRender: ({ record }) => {
      return `满${get(record, 'operationCoupon.threshold')}减${get(record, 'operationCoupon.price')}`;
    },
  },
  {
    title: '有效期',
    dataIndex: 'beginDate',
    customRender: ({ record }) => {
      return `${get(record, 'operationCoupon.beginDate')}至${get(record, 'operationCoupon.endDate')}`;
    },
  },
  {
    title: '发布时间',
    dataIndex: 'createTime',
  },
  {
    title: '状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      return showToBadge({
        text,
        arr: UserCouponStateArray,
      });
    },
  },
];

export const userEducationColumns: BasicColumn[] = [
  {
    title: '申请学历',
    dataIndex: 'education',
    customRender: ({ text }) => {
      return showToTag({ text, arr: EducationArray });
    },
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '申请时间',
    dataIndex: 'submitTime',
  },
  {
    title: '审批状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      return showToBadge({ text, arr: AuditStatusArray });
    },
  },
  {
    title: '审批人',
    dataIndex: 'handleUserName',
  },
  {
    title: '审批时间',
    dataIndex: 'handlerTime',
  },
];
export const userApartmentColumns: BasicColumn[] = [
  {
    title: '申请公寓',
    dataIndex: 'apartmentName',
  },
  {
    title: '申请户型',
    dataIndex: 'subTarget',
  },
  {
    title: '申请时间',
    dataIndex: 'submitTime',
  },
  {
    title: '审批状态',
    dataIndex: 'state',
    customRender: ({ text }) => {
      return showToBadge({ text, arr: AuditStatusArray });
    },
  },
  {
    title: '审批人',
    dataIndex: 'handleUserName',
  },
  {
    title: '审批时间',
    dataIndex: 'handlerTime',
  },
];
