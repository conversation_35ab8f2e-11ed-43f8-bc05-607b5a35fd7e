<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { memberColumns, memberSearchSchema } from './data';
  import { apiMemberPage, apiMemberBlacklist } from '@/api/operation/member';
  import { useGo } from '@/hooks/web/usePage';
  import { PageEnum } from '@/enums/pageEnum';
  import { Modal, message } from 'ant-design-vue';
  import { to } from '@bryce-loskie/utils';

  const go = useGo();

  const [registerTable, { reload }] = useTable({
    columns: memberColumns,
    api: apiMemberPage,
    useSearchForm: true,
    formConfig: {
      schemas: memberSearchSchema,
    },
    actionColumn: {
      width: 170,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const method = {
    goToInfo(record: Recordable) {
      go(`${PageEnum.MEMBER_INFO}/${record.id}`);
    },
  };

  const handleSetBlack = async (record: Recordable) => {
    const currentIsBlack = record.state === 0;
    const title = currentIsBlack ? '解除设置' : '设置为黑名单';
    const content = currentIsBlack
      ? '确定要解除设置吗？解除后，该用户将可以正常使用系统功能。'
      : `确定要将当前用户设置为黑名单吗？设置后，该用户将无法使用系统的任何功能。`;

    const modalIns = Modal.confirm({
      type: currentIsBlack ? 'success' : 'warning',
      title,
      content,
      closable: true,
      centered: true,
      maskClosable: true,
      onOk: async () => {
        modalIns.update({
          okButtonProps: { loading: true },
          cancelButtonProps: { disabled: true },
        });
        const [err] = await to(apiMemberBlacklist({ id: record.id, state: !currentIsBlack }));
        modalIns.update({
          okButtonProps: { loading: false },
          cancelButtonProps: { disabled: false },
        });
        if (err) {
          return;
        }
        message.success('操作成功');
        reload();
      },
    });
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: method.goToInfo.bind(null, record),
      },
      {
        label: '解除黑名单限制',
        ifShow() {
          return record.state === 0;
        },
        onClick: handleSetBlack.bind(null, record),
      },
      {
        label: '设置为黑名单',
        ifShow() {
          const { state } = record;
          return state === null || state === 1;
        },
        danger: true,
        onClick: handleSetBlack.bind(null, record),
      },
    ];
  }
</script>

<template>
  <page-wrapper title="注册用户管理">
    <template #extra>
      <a-button type="primary">导出</a-button>
    </template>
    <div>
      <BasicTable @register="registerTable">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="tableAction(record)" />
          </template>
        </template>
      </BasicTable>
    </div>
  </page-wrapper>
</template>

<style lang="less" scoped>
  .test {
    height: 100%;
    padding-top: 20px;
    padding-right: 20px;
    padding-left: 20px;

    &-content {
      height: 100%;
      overflow: hidden;
      overflow-y: auto;
      background-color: red;
    }
  }
</style>
