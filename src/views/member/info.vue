<script lang="ts" setup>
  // 获取路由
  import { useRoute } from 'vue-router';
  import BasicUser from '@/components/Business/BasicUser.vue';
  import DividerTitle from '@/components/Form/src/extend/DividerTitle.vue';
  import { Tabs } from 'ant-design-vue';
  import { Description } from '@/components/Description';
  import { apiMemberInfo } from '@/api/operation/member';
  import { get } from 'lodash-es';
  import { computed } from 'vue';
  import {
    basicMemberDesc,
    educationMemberDesc,
    enterpriseMemberDesc,
    backMemberDesc,
    registerMemberDesc,
  } from './data';
  import { formatFields } from '@/utils/business';
  import { useGo } from '@/hooks/web/usePage';
  import UserCoupon from './user-coupon.vue';
  import UserApartment from './user-apartment.vue';
  import UserEducation from './user-education.vue';
  import UserEmployment from './user-employment.vue';
  import { useApiLoading } from '@/hooks/web/useApiLoading';

  const go = useGo();
  const route = useRoute();
  // 获取数据

  // const data = ref<any>({});
  const pageId = computed(() => get(route, 'params.id') as string);

  const { apiResult: data, loading } = useApiLoading({
    api: apiMemberInfo,
    params: route.params,
  });

  const renderName = computed(() => {
    return formatFields(data.value, ['name', 'phone']);
  });
</script>

<template>
  <page-wrapper v-loading="loading" @back="() => go(-1)" title="详情">
    <template #headerContent>
      <BasicUser :username="renderName" :photo="get(data, 'photo')" />
    </template>

    <Tabs default-active-key="1">
      <Tabs.TabPane key="1" tab="基本信息">
        <div>
          <div class="mb-4">
            <DividerTitle :line="false" title="基础信息" />
            <a-card>
              <Description :column="2" :schema="basicMemberDesc" :data="data" :bordered="false" />
            </a-card>
          </div>
          <div class="mb-4">
            <DividerTitle :line="false" title="学历信息" />
            <a-card>
              <Description
                :column="2"
                :schema="educationMemberDesc"
                :data="data"
                :bordered="false"
              />
            </a-card>
          </div>
          <div class="mb-4">
            <DividerTitle :line="false" title="单位信息" />
            <a-card>
              <Description
                :column="2"
                :schema="enterpriseMemberDesc"
                :data="data"
                :bordered="false"
              />
            </a-card>
          </div>
          <div class="mb-4">
            <DividerTitle :line="false" title="银行信息" />
            <a-card>
              <Description :column="2" :schema="backMemberDesc" :data="data" :bordered="false" />
            </a-card>
          </div>
          <div class="mb-4">
            <DividerTitle :line="false" title="注册信息" />
            <a-card>
              <Description
                :column="2"
                :schema="registerMemberDesc"
                :data="data"
                :bordered="false"
              />
            </a-card>
          </div>
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="2" tab="消费券">
        <div>
          <UserCoupon :member-id="pageId" />
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="3" tab="人才补贴">
        <div>
          <UserEducation :member-id="pageId" />
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="4" tab="就业补贴">
        <div>
          <UserEmployment :member-id="pageId" />
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="5" tab="人才公寓">
        <div>
          <UserApartment :member-id="pageId" />
        </div>
      </Tabs.TabPane>
    </Tabs>
  </page-wrapper>
</template>

<style lang="less" scoped></style>
