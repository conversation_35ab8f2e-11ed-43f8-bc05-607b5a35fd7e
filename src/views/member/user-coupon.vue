<template>
  <div>
    <BasicTable @register="registerTable" />
  </div>
</template>

<script lang="ts" setup>
  import { userCouponColumns } from './data';
  import { BasicTable, useTable } from '@/components/Table';
  import { apiMemberCouponPage } from '@/api/operation/member';

  interface Props {
    memberId: string;
  }

  const props = defineProps<Props>();
  const [registerTable] = useTable({
    columns: userCouponColumns,
    api: apiMemberCouponPage,
    useSearchForm: false,
    searchInfo: {
      categoryType: 'COUPON',
      memberId: props.memberId,
    },
  });
</script>

<style lang="less" scoped></style>
