<template>
  <div>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" setup>
  import { userEducationColumns } from './data';
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { apiAuditPage } from '@/api/operation/audit';
  import { useGo } from '@/hooks/web/usePage';

  interface Props {
    memberId: string;
  }

  const props = defineProps<Props>();
  const go = useGo();
  const [registerTable] = useTable({
    columns: userEducationColumns,
    api: apiAuditPage,
    useSearchForm: false,
    searchInfo: {
      type: 'education',
      memberId: props.memberId,
    },
    actionColumn: {
      width: 100,
      title: '操作',
      dataIndex: 'action',
    },
  });

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '详情',
        onClick: () => {
          go(`/member/info-audit/${record.id}`);
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
