import { previewImage } from '@/components/RenderVnode';
import { BasicColumn, FormSchema } from '@/components/Table';

export const basicColumns: BasicColumn[] = [
  {
    title: '海报',
    dataIndex: 'url',
    customRender: ({ text }) => {
      return previewImage(text, 'miniTable');
    },
  },
  {
    title: '排序',
    dataIndex: 'sort',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'url',
    label: '海报',
    component: 'UploadImage',
    required: true,
  },
  {
    field: 'sort',
    label: '排序',
    component: 'InputNumber',
    defaultValue: 0,
    required: true,
    componentProps: {
      min: 0,
      max: 999,
      // 不能 0.1
      precision: 0,
    },
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];
