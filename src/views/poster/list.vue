<template>
  <page-wrapper title="海报管理">
    <template #extra>
      <a-button preIcon="ant-design:plus-outlined" type="primary" @click="method.add()">
        新增海报
      </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawer" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';
  import { basicColumns, basicSchema } from './data';
  import { apiPosterPage, apiPosterAdd, apiPosterDel } from '@/api/operation/poster';

  const [registerTable, { reload }] = useTable({
    columns: basicColumns,
    api: apiPosterPage,
    useSearchForm: false,
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerDrawer, { addDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiPosterAdd,
  });

  const method = {
    add() {
      addDrawer({
        title: '新增海报',
      });
    },
    async del(_record: Recordable) {
      await apiPosterDel(_record.id);
      reload();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
