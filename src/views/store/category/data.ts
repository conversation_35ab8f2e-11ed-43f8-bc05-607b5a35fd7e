import { BasicColumn, FormSchema } from '@/components/Table';

export const listColumns: BasicColumn[] = [
  {
    title: '分类名称',
    dataIndex: 'name',
  },

  {
    title: '分类描述',
    dataIndex: 'remark',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const categorySchema: FormSchema[] = [
  {
    field: 'name',
    label: '分类名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'remark',
    label: '分类描述',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 100,
      showCount: true,
    },
  },
  {
    field: 'type',
    label: '分类类型',
    component: 'Input',
    show: false,
    defaultValue: 'STORE',
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const searchSchema: FormSchema[] = [];
