<template>
  <div>
    <SModal
      :loading="loading"
      @register="registerModal"
      is-control
      @on-ok="method.handeUpload"
      @on-cancel="method.cancel"
      title="商家导入"
      :ok-text="renderProgressText"
    >
      <a-row :gutter="[16, 16]">
        <a-col :span="24">
          <div> 1.下载导入模板 </div>
        </a-col>
        <a-col :span="24">
          <div>
            <a-button
              preIcon="material-symbols:cloud-download"
              type="default"
              @click="
                () => {
                  downloadByUrl({
                    url: 'https://tzcjlw.oss-cn-shanghai.aliyuncs.com/mini-program/store_template.xlsx',
                  });
                }
              "
            >
              下载空的模板表格
            </a-button>
          </div>
        </a-col>
        <a-col :span="24">
          <div> 2.上传完善后的模板 </div>
        </a-col>
        <a-col :span="24">
          <div
            :class="{
              'import-excel-disabled': loading,
            }"
          >
            <div v-if="!isFile" @click="method.selectFile" class="import-excel-card-upload">
              <Icon size="30" class="icon" icon="vscode-icons:file-type-excel2" />
              <div class="title">选择文件</div>
              <div class="des">下载模板并完善信息后，可到此处进行上传，支持格式：XLSX</div>
            </div>
            <div v-else @click="method.selectFile" class="import-excel-card-upload">
              <Icon size="30" class="icon" icon="vscode-icons:file-type-excel2" />
              <div class="title">重新上传</div>
              <div class="title">当前文件：{{ get(fileConfig, 'name') }}</div>
              <!-- <div class="des">下载模板并完善信息后，可到此处进行上传，支持格式：XLSX</div> -->
            </div>
          </div>
        </a-col>
      </a-row>
    </SModal>
    <div>
      <input
        ref="fileInputEl"
        type="file"
        @change="onFileChange"
        class="import-excel-input"
        accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { SModal, useSModalInner } from '@/components/SModal';
  import Icon from '@/components/Icon/Icon.vue';
  import { useTemplateRef, shallowRef, computed, unref, h } from 'vue';
  import { get, size } from 'lodash-es';
  import { message, Modal, List } from 'ant-design-vue';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { apiStoreImport } from '@/api/operation/store';
  import { downloadByUrl } from '@/utils/file/download';

  defineEmits(['register']);
  const fileInput = useTemplateRef('fileInputEl');
  // const isFile = ref(false);
  const fileConfig = shallowRef();

  const isFile = computed(() => {
    return !!fileConfig.value;
  });

  const { loading, progress, reload } = useApiLoading({
    api: apiStoreImport,
    immediate: false,
    type: 'file',
  });
  // 渲染进度文本
  const renderProgressText = computed(() => {
    if (unref(loading)) {
      return `上传中 ${unref(progress)}%`;
    }
    return '上传';
  });

  const [registerModal, { closeModal }] = useSModalInner({
    open() {
      console.log('open');
      cleanFileInput();
    },
  });

  function onFileChange(e) {
    const files = e.target.files;
    console.log(files);
    if (size(files) === 0) {
      return;
    }

    if (size(files) !== 1) {
      e.target.value = null;
      // isFile.value = false;
      return message.error('一次只能上传一个文件');
    }
    // isFile.value = true;
    fileConfig.value = files[0];
  }

  function cleanFileInput() {
    fileConfig.value = null;
    fileInput.value && fileInput.value.files && (fileInput.value.files = null);
    fileInput.value && fileInput.value.value && (fileInput.value.value = '');
  }

  const method = {
    selectFile() {
      fileInput.value?.click();
    },
    // 上传
    handeUpload() {
      console.log('上传');
      if (!unref(isFile)) {
        return message.error('请选择文件');
      }
      // 开始上传
      reload({
        file: unref(fileConfig),
      }).then((res) => {
        console.log(res);
        if (get(res, 'code') === 1) {
          // 有错误
          // 获取错误信息
          const err = get(res, 'data');

          if (size(err) > 0) {
            Modal.error({
              title: '导入提示',
              content: () =>
                h('div', {}, [
                  h(
                    'div',
                    {
                      style: {
                        marginBottom: '10px',
                      },
                    },
                    `导入失败${size(err)}个商家`,
                  ),
                  h(
                    'div',
                    {
                      style: {
                        marginBottom: '5px',
                        color: '#999999',
                      },
                    },
                    `以下为导入失败情况总结：`,
                  ),
                  h(List, {
                    bordered: true,
                    dataSource: err,
                    renderItem: ({ item, index }) => h(List.Item, {}, () => `${index + 1}.${item}`),
                  }),
                ]),
              // content: () =>
              //   h(List, {
              //     bordered: true,
              //     dataSource: err,
              //     renderItem: ({ item, index }) => h(List.Item, {}, () => `${index + 1}.${item}`),
              //   }),
            });
          } else {
            // 其他错误
            Modal.error({
              title: '导入失败',
              content: '导入出错',
            });
          }
        } else {
          // 没有错误
          Modal.success({
            title: '导入成功',
            content: '导入成功',
          });
        }
        closeModal();
      });
    },
    cancel() {
      closeModal();
    },
  };
</script>

<style lang="less" scoped>
  .import-excel {
    &-input {
      display: none !important;
    }

    &-disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }

    &-card-upload {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 140px;
      padding: 20px;
      // 过渡
      transition: all 0.3s ease-in-out;
      border: 1px dashed #d9d9d9;
      border-radius: 4px;
      cursor: pointer;

      &:hover {
        border-color: @link-hover-color;
        color: @link-hover-color;
      }

      &:active {
        border-color: @link-active-color;
        color: @link-active-color;
      }

      & .icon {
        color: #d9d9d9;
        font-size: 40px;
      }

      & .title {
        font-size: 16px;
      }

      & .des {
        color: #d9d9d9;
        font-size: 12px;
      }
    }
  }
</style>
