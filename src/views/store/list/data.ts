import { BasicColumn, FormSchema } from '@/components/Table';
import { apiCategoryList } from '@/api/operation/category';
import { DescItem } from '@/components/Description';
import { get } from 'lodash-es';
import { phoneReg } from '@/utils/validate';
import { statusBadge } from '@/components/RenderVnode';
import { StatusStoreArray } from '@/maps/sysMaps';

export const listColumns: BasicColumn[] = [
  {
    title: '商家名称',
    dataIndex: 'title',
  },
  {
    title: '商家类型',
    dataIndex: 'categoryNames',
  },
  {
    title: '联系人',
    dataIndex: 'linkman',
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
  },
  {
    title: '商家地址',
    dataIndex: 'address',
  },
  {
    title: '状态',
    dataIndex: 'state',
    customRender: ({ text }) => statusBadge({ text, arr: StatusStoreArray }),
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
];

export const basicSchema: FormSchema[] = [
  {
    field: 'photo',
    label: '商家图片',
    component: 'UploadImage',
  },
  {
    field: 'title',
    label: '商家名称',
    component: 'Input',
    required: true,
  },
  {
    field: 'categoryIdList',
    label: '商家类型',
    component: 'ApiSelect',
    required: true,
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'STORE' });
      },
      labelField: 'name',
      valueField: 'id',
      mode: 'multiple',
    },
  },

  {
    field: 'linkman',
    label: '联系人',
    component: 'Input',
    required: true,
  },
  {
    field: 'phone',
    label: '联系电话',
    component: 'Input',
    required: true,
    // dynamicDisabled: ({ model }) => {
    //   return !!model.id;
    // },
    rules: [phoneReg],
  },
  {
    field: 'address',
    label: '商家地址',
    component: 'InputTextArea',
    required: true,
    componentProps: {
      maxlength: 100,
      showCount: true,
    },
  },
  {
    field: 'sort',
    label: '排序',
    component: 'InputNumber',
    required: true,
    defaultValue: 0,
    componentProps: {
      min: 0,
      // 不支持小数点
      precision: 0,
      max: 999999,
    },
  },
  {
    field: 'state',
    label: '状态',
    component: 'RadioButtonGroup',
    componentProps: {
      options: StatusStoreArray,
    },
    required: true,
  },
  {
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const searchSchema: FormSchema[] = [
  {
    field: 'categoryId',
    label: '消费券分类',
    component: 'ApiSelect',
    componentProps: {
      api: () => {
        return apiCategoryList({ categoryType: 'STORE' });
      },
      labelField: 'name',
      valueField: 'id',
    },
  },
  {
    field: 'title',
    label: '商家名称',
    component: 'Input',
  },
];

export const basicDesc: DescItem[] = [
  {
    label: '商家名称',
    field: 'title',
  },
  {
    label: '商家类型',
    field: 'categoryNames',
  },
  {
    label: '联系人',
    field: 'linkman',
  },
  {
    label: '联系电话',
    field: 'phone',
  },
  {
    label: '状态',
    field: 'state',
    render: (text) => statusBadge({ text, arr: StatusStoreArray }),
  },
  {
    label: '商家地址',
    field: 'address',
  },
  {
    label: '创建人',
    field: 'createBy',
  },
  {
    label: '创建时间',
    field: 'createTime',
  },
];

export const settleColumns: BasicColumn[] = [
  {
    title: '核销券额',
    dataIndex: 'title',
    customRender: ({ record }) => {
      return `满${get(record, 'operationCoupon.threshold')}减${get(record, 'operationCoupon.price')}`;
    },
  },
  {
    title: '核销券码',
    dataIndex: 'code',
    customRender: ({ record }) => {
      return get(record, 'operationCoupon.id', '--');
    },
  },
  {
    title: '核销商家',
    dataIndex: 'createBy',
    customRender: ({ record }) => {
      return get(record, 'operationStore.title', '--');
    },
  },
  {
    title: '核销时间',
    dataIndex: 'useTime',
  },
  {
    title: '用户昵称',
    dataIndex: 'nickname',
    customRender: ({ record }) => {
      return get(record, 'operationMember.nickname', '--');
    },
  },
  {
    title: '用户手机号',
    dataIndex: 'phone',
    customRender: ({ record }) => {
      return get(record, 'operationMember.phone', '--');
    },
  },
];
