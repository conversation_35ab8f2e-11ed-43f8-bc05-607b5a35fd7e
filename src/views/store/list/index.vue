<template>
  <page-wrapper title="商家管理">
    <template #extra>
      <a-space>
        <a-button preIcon="ant-design:plus-outlined" type="primary" @click="method.add()">
          新增商家
        </a-button>
        <a-button preIcon="ant-design:import-outlined" @click="openModal()"> 导入商家 </a-button>
      </a-space>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawer" @success="reload()" />
    <importExcel @register="registerModel" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';
  import { useSModal } from '@/components/SModal';
  import { listColumns, basicSchema, searchSchema } from './data';
  import {
    apiStorePage,
    apiStoreAdd,
    apiStoreEdit,
    apiStoreDelete,
    apiStorePutAway,
  } from '@/api/operation/store';
  import { useGo } from '@/hooks/web/usePage';
  // import { PageEnum } from '@/enums/pageEnum';
  import importExcel from './components/import-excel.vue';

  const go = useGo();

  const [registerTable, { reload }] = useTable({
    columns: listColumns,
    formConfig: {
      schemas: searchSchema,
    },
    api: apiStorePage,
    useSearchForm: true,
    actionColumn: {
      width: 200,
      title: '操作',
      dataIndex: 'action',
    },
  });
  const [registerModel, { openModal }] = useSModal();

  const [registerDrawer, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    addFn: apiStoreAdd,
    updateFn: apiStoreEdit,
  });

  const method = {
    add() {
      addDrawer({
        title: '新增商家',
      });
    },
    edit(record: Recordable) {
      updateDrawer({
        record,
      });
    },
    async del(record: Recordable) {
      await apiStoreDelete(record);
      reload();
    },
    info(record: Recordable) {
      // go({
      //   path: PageEnum.STORE_INFO,
      //   query: {
      //     id: record.id,
      //   },
      // });
      go(`/store/list/info/${record.id}`);
    },
    // 上下架
    async state(record: Recordable) {
      await apiStorePutAway({
        id: record.id,
        state: !record.state,
      });
      reload();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.edit.bind(null, record),
      },
      {
        label: '详情',
        onClick: method.info.bind(null, record),
      },
      {
        label: '下架',
        popConfirm: {
          title: '确定下架吗？',
          confirm: method.state.bind(null, record),
        },
        ifShow: () => record.state,
      },
      {
        label: '上架',
        popConfirm: {
          title: '确定上架吗？',
          confirm: method.state.bind(null, record),
        },
        ifShow: () => !record.state,
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
