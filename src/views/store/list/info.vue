<script lang="ts" setup>
  // 获取路由
  import { useRoute } from 'vue-router';
  import BasicUser from '@/components/Business/BasicUser.vue';
  import DividerTitle from '@/components/Form/src/extend/DividerTitle.vue';
  import { Tabs, RadioGroup, RadioButton } from 'ant-design-vue';
  import { Description } from '@/components/Description';
  import { apiMemberCouponPage } from '@/api/operation/member';
  import { get } from 'lodash-es';
  import { computed, reactive, watch, toRaw } from 'vue';
  import { basicDesc, settleColumns, basicSchema } from './data';
  import { formatFields } from '@/utils/business';
  import { BasicTable, useTable } from '@/components/Table';
  import { apiStoreInfo, apiStoreEdit } from '@/api/operation/store';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';
  import { useApiLoading } from '@/hooks/web/useApiLoading';
  import { statusBadge } from '@/components/RenderVnode';
  import { StatusStoreArray } from '@/maps/sysMaps';

  const route = useRoute();

  const searchInfo = reactive({
    closing: 'true',
    storeId: get(route, 'params.id'),
  });

  const {
    apiResult: data,
    loading,
    reload: reloadData,
  } = useApiLoading({
    api: apiStoreInfo,
    params: route.params,
  });

  // const data = ref<any>({});
  watch(
    () => searchInfo.closing,
    () => {
      reload && reload();
    },
  );

  const [registerTable, { reload }] = useTable({
    columns: settleColumns,
    api: apiMemberCouponPage,
    useSearchForm: false,
  });

  const [registerDrawer, { updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    updateFn: apiStoreEdit,
  });

  const methods = {
    // async fetchData() {
    //   loadingRef.value = true;
    //   try {
    //     data.value = await apiStoreInfo(get(route, 'query'));
    //     loadData.value = true;
    //   } finally {
    //     loadingRef.value = false;
    //   }
    // },
    edit() {
      updateDrawer({
        title: '编辑商家信息',
        record: toRaw(data.value),
      });
    },
  };

  const renderName = computed(() => {
    return formatFields(data.value, ['title']);
  });

  // methods.fetchData();
</script>

<template>
  <page-wrapper v-loading="loading" @back="() => $router.go(-1)" title="详情">
    <template #extra>
      <a-button
        v-if="!loading"
        preIcon="ant-design:edit-outlined"
        type="primary"
        @click="methods.edit()"
      >
        编辑
      </a-button>
    </template>
    <template #headerContent>
      <BasicUser :avatar="get(data, 'photo')" :username="renderName">
        <component :is="statusBadge({ text: get(data, 'state'), arr: StatusStoreArray })" />
      </BasicUser>
    </template>
    <Tabs default-active-key="1">
      <Tabs.TabPane key="1" tab="基本信息">
        <div>
          <div class="mb-4">
            <DividerTitle :line="false" title="基础信息" />
            <a-card>
              <Description :column="2" :schema="basicDesc" :data="data" :bordered="false" />
            </a-card>
          </div>
        </div>
      </Tabs.TabPane>
      <Tabs.TabPane key="2" tab="核销记录">
        <div>
          <div class="mb-4">
            <BasicTable :search-info="searchInfo" @register="registerTable">
              <template #tableTitle>
                <div>
                  <RadioGroup v-model:value="searchInfo.closing" button-style="solid">
                    <RadioButton value="true">已结算</RadioButton>
                    <RadioButton value="false">未结算</RadioButton>
                  </RadioGroup>
                </div>
              </template>
            </BasicTable>
          </div>
        </div>
      </Tabs.TabPane>
    </Tabs>
    <SDrawerForm @register="registerDrawer" @success="reloadData()" />
  </page-wrapper>
</template>

<style lang="less" scoped></style>
