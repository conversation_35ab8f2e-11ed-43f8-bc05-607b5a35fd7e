<template>
  <page-wrapper title="消费券管理">
    <template #extra>
      <a-button preIcon="ant-design:plus-outlined" type="primary" @click="method.add()">
        新增分类
      </a-button>
    </template>
    <BasicTable @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'action'">
          <TableAction :actions="tableAction(record)" />
        </template>
      </template>
    </BasicTable>
    <SDrawerForm @register="registerDrawer" @success="reload()" />
  </page-wrapper>
</template>

<script lang="ts" setup>
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { useSDrawerForm, SDrawerForm } from '@/components/SDrawer';
  import { basicColumns, basicSchema } from './data';

  const [registerTable, { reload }] = useTable({
    columns: basicColumns,
    api: async () => [],
    useSearchForm: false,
    searchInfo: {
      categoryType: 'COUPON',
    },
    actionColumn: {
      width: 160,
      title: '操作',
      dataIndex: 'action',
    },
  });

  const [registerDrawer, { addDrawer, updateDrawer }] = useSDrawerForm({
    schemas: basicSchema,
    updateFn: () => {},
  });

  const method = {
    add() {
      addDrawer({
        title: '新增消费券分类',
      });
    },
    edit(record: Recordable) {
      updateDrawer({
        record,
      });
    },
    async del(_record: Recordable) {
      reload();
    },
  };

  function tableAction(record: Recordable): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: method.edit.bind(null, record),
      },
      {
        label: '删除',
        danger: true,
        popConfirm: {
          title: '确定删除吗？',
          confirm: method.del.bind(null, record),
        },
      },
    ];
  }
</script>

<style lang="less" scoped></style>
