import type { ComputedRef, Ref } from 'vue';

export type DynamicProps<T> = {
  [P in keyof T]: Ref<T[P]> | T[P] | ComputedRef<T[P]>;
};

export type CreateMapParamsExtend =
  | Record<string, any>
  | string
  | Array<any>
  | undefined
  | number
  | boolean
  | null;

export type CreateMapParams = [any, string, string, CreateMapParamsExtend?];

// 返回值
export interface CreateMapReturnValue {
  value: any;
  label: string;
  color: string;
  extend?: CreateMapParamsExtend;
}

export type CreateMapReturn = CreateMapReturnValue[];

// 扩展 CreateMapReturnValue
export interface ExtendCreateMapReturnValue extends CreateMapReturnValue {
  // 是否查询成功
  success: boolean;
}
