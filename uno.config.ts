import {
  defineConfig,
  presetTypography,
  presetUno,
  toEscapedSelector as e,
  presetIcons,
  Preset,
} from 'unocss';

function presetEllipsis(): Preset {
  return {
    name: 'uno-preset-ellipsis',
    autocomplete: {
      templates: ['ellipsis-(2|3|4|5|6|7|8|9|10)'],
    },
    rules: [
      [
        /^ellipsis-(\d+)/,
        ([, lineCount], { rawSelector }) => {
          const selector = e(rawSelector);
          return `
        ${selector} {
          display: -webkit-box;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: ${lineCount};
          line-break: auto;
          -webkit-box-orient: vertical;
        }
      `;
        },
      ],
    ],
  };
}

export default defineConfig({
  theme: {
    colors: {
      primary: '#0960bd',
    },
  },
  presets: [
    presetEllipsis(),
    presetUno(),
    presetTypography(),
    presetIcons({
      cdn: 'https://esm.sh/',
      prefix: 'i-',
      extraProperties: {
        display: 'inline-block',
        'vertical-align': 'middle',
      },
    }),
  ],
});
